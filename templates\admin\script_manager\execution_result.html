{% extends "admin/base_site.html" %}
{% load i18n admin_urls static %}

{% block title %}执行结果: {{ record.execution_id }}{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
    <a href="{% url 'admin:index' %}">首页</a>
    &rsaquo; <a href="{% url 'admin:script_manager_executionrecord_changelist' %}">执行记录</a>
    &rsaquo; 执行结果: {{ record.execution_id }}
</div>
{% endblock %}

{% block content %}
<div class="module aligned">
    <h1>执行结果: {{ record.execution_id }}</h1>

    <fieldset class="module aligned">
        <h2>基本信息</h2>
        <div class="form-row">
            <div class="field-box">
                <label>脚本名称:</label>
                <p>{{ record.script.name }}</p>
            </div>
        </div>
        <div class="form-row">
            <div class="field-box">
                <label>执行状态:</label>
                <p class="status-{{ record.status }}">
                    {% if record.status == 'success' %}✓ 成功
                    {% elif record.status == 'failed' %}✗ 失败
                    {% elif record.status == 'running' %}⏳ 运行中
                    {% elif record.status == 'pending' %}⏸ 等待中
                    {% elif record.status == 'cancelled' %}⏹ 已取消
                    {% endif %}
                </p>
            </div>
        </div>
        <div class="form-row">
            <div class="field-box">
                <label>触发方式:</label>
                <p>{{ record.get_trigger_type_display }}</p>
            </div>
        </div>
        {% if record.triggered_by %}
        <div class="form-row">
            <div class="field-box">
                <label>触发者:</label>
                <p>{{ record.triggered_by.username }}</p>
            </div>
        </div>
        {% endif %}
        <div class="form-row">
            <div class="field-box">
                <label>开始时间:</label>
                <p>{{ record.started_at|default:"未开始" }}</p>
            </div>
        </div>
        <div class="form-row">
            <div class="field-box">
                <label>结束时间:</label>
                <p>{{ record.finished_at|default:"未结束" }}</p>
            </div>
        </div>
        {% if record.duration %}
        <div class="form-row">
            <div class="field-box">
                <label>执行时长:</label>
                <p>{{ record.duration }}</p>
            </div>
        </div>
        {% endif %}
        {% if record.return_code is not None %}
        <div class="form-row">
            <div class="field-box">
                <label>返回码:</label>
                <p>{{ record.return_code }}</p>
            </div>
        </div>
        {% endif %}
    </fieldset>

    {% if record.parameters %}
    <fieldset class="module aligned">
        <h2>执行参数</h2>
        <pre>{{ record.parameters|pprint }}</pre>
    </fieldset>
    {% endif %}

    <!-- 新增：HTML结果显示区域 -->
    <fieldset class="module aligned">
        <h2>执行结果</h2>
        {% if has_html_result %}
            <div class="html-result-container">
                {{ html_result_content|safe }}
            </div>
        {% else %}
            <div class="empty-result-message">
                <p>📄 暂无HTML结果文件</p>
                <p class="help-text">脚本需要生成名为"结果正文.html"的文件来显示结果内容</p>
            </div>
        {% endif %}
    </fieldset>

    {% if record.stdout %}
    <fieldset class="module aligned">
        <h2>标准输出</h2>
        <pre class="output-content">{{ record.stdout }}</pre>
    </fieldset>
    {% endif %}

    {% if record.stderr %}
    <fieldset class="module aligned">
        <h2>错误输出</h2>
        <pre class="error-content">{{ record.stderr }}</pre>
    </fieldset>
    {% endif %}

    {% if output_files %}
    <fieldset class="module aligned">
        <h2>输出文件</h2>
        <ul class="output-files">
            {% for file in output_files %}
            {% if file.name %}
            <li>
                <a href="{% url 'script_manager:download_output_file' record.id file.name %}"
                   class="file-download">
                    {{ file.name }} ({{ file.size|filesizeformat }})
                </a>
            </li>
            {% endif %}
            {% endfor %}
        </ul>
    </fieldset>
    {% endif %}

    {% if stats %}
    <fieldset class="module aligned">
        <h2>脚本统计</h2>
        <div class="form-row">
            <div class="field-box">
                <label>总执行次数:</label>
                <p>{{ stats.total }}</p>
            </div>
        </div>
        <div class="form-row">
            <div class="field-box">
                <label>成功次数:</label>
                <p>{{ stats.success }}</p>
            </div>
        </div>
        <div class="form-row">
            <div class="field-box">
                <label>失败次数:</label>
                <p>{{ stats.failed }}</p>
            </div>
        </div>
        <div class="form-row">
            <div class="field-box">
                <label>成功率:</label>
                <p>{{ stats.success_rate|floatformat:2 }}%</p>
            </div>
        </div>
    </fieldset>
    {% endif %}

    <div class="submit-row">
        <a href="{% url 'admin:script_manager_executionrecord_changelist' %}" class="button">返回列表</a>
        {% if record.is_running %}
        <a href="{% url 'script_manager:cancel_execution' record.id %}"
           class="button" onclick="return confirm('确定要取消执行吗？')">取消执行</a>
        {% endif %}
    </div>
</div>

<style>
.output-content, .error-content {
    background: #f8f8f8;
    border: 1px solid #ddd;
    padding: 10px;
    max-height: 400px;
    overflow-y: auto;
    font-family: monospace;
    white-space: pre-wrap;
}

.error-content {
    background: #fff5f5;
    border-color: #f5c6cb;
    color: #721c24;
}

.status-success { color: #28a745; }
.status-failed { color: #dc3545; }
.status-running { color: #007bff; }
.status-pending { color: #6c757d; }
.status-cancelled { color: #6c757d; }

.output-files {
    list-style: none;
    padding: 0;
}

.output-files li {
    margin: 5px 0;
}

.file-download {
    display: inline-block;
    padding: 5px 10px;
    background: #007bff;
    color: white;
    text-decoration: none;
    border-radius: 3px;
}

.file-download:hover {
    background: #0056b3;
    color: white;
}

/* HTML结果显示样式 */
.html-result-container {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    background: #fff;
    max-height: 600px;
    overflow-y: auto;
    margin: 10px 0;
}

.html-result-container table {
    width: 100%;
    border-collapse: collapse;
    margin: 10px 0;
}

.html-result-container table th,
.html-result-container table td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}

.html-result-container table th {
    background-color: #f2f2f2;
    font-weight: bold;
}

.empty-result-message {
    text-align: center;
    padding: 40px 20px;
    background: #f9f9f9;
    border: 1px dashed #ccc;
    border-radius: 4px;
    color: #666;
}

.empty-result-message p {
    margin: 10px 0;
}

.empty-result-message .help-text {
    font-size: 0.9em;
    color: #999;
}


</style>
{% endblock %}
