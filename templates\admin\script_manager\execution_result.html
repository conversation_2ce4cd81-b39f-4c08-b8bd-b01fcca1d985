{% extends "admin/base_site.html" %}
{% load i18n admin_urls static %}

{% block title %}执行结果: {{ record.execution_id }}{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
    <a href="{% url 'admin:index' %}">首页</a>
    &rsaquo; <a href="{% url 'admin:script_manager_executionrecord_changelist' %}">执行记录</a>
    &rsaquo; 执行结果: {{ record.execution_id }}
</div>
{% endblock %}

{% block content %}
<div class="module aligned">
    <h1>执行结果: {{ record.execution_id }}</h1>

    <fieldset class="module aligned">
        <h2>基本信息</h2>
        <div class="form-row">
            <div class="field-box">
                <label>脚本名称:</label>
                <p>{{ record.script.name }}</p>
            </div>
        </div>
        <div class="form-row">
            <div class="field-box">
                <label>执行状态:</label>
                <p class="status-{{ record.status }}">
                    {% if record.status == 'success' %}✓ 成功
                    {% elif record.status == 'failed' %}✗ 失败
                    {% elif record.status == 'running' %}⏳ 运行中
                    {% elif record.status == 'pending' %}⏸ 等待中
                    {% elif record.status == 'cancelled' %}⏹ 已取消
                    {% endif %}
                </p>
            </div>
        </div>
        <div class="form-row">
            <div class="field-box">
                <label>触发方式:</label>
                <p>{{ record.get_trigger_type_display }}</p>
            </div>
        </div>
        {% if record.triggered_by %}
        <div class="form-row">
            <div class="field-box">
                <label>触发者:</label>
                <p>{{ record.triggered_by.username }}</p>
            </div>
        </div>
        {% endif %}
        <div class="form-row">
            <div class="field-box">
                <label>开始时间:</label>
                <p>{{ record.started_at|default:"未开始" }}</p>
            </div>
        </div>
        <div class="form-row">
            <div class="field-box">
                <label>结束时间:</label>
                <p>{{ record.finished_at|default:"未结束" }}</p>
            </div>
        </div>
        {% if record.duration %}
        <div class="form-row">
            <div class="field-box">
                <label>执行时长:</label>
                <p>{{ record.duration }}</p>
            </div>
        </div>
        {% endif %}
        {% if record.return_code is not None %}
        <div class="form-row">
            <div class="field-box">
                <label>返回码:</label>
                <p>{{ record.return_code }}</p>
            </div>
        </div>
        {% endif %}
    </fieldset>

    {% if record.parameters %}
    <fieldset class="module aligned">
        <h2>执行参数</h2>
        <pre>{{ record.parameters|pprint }}</pre>
    </fieldset>
    {% endif %}

    <!-- 新增：HTML结果显示区域 -->
    <fieldset class="module aligned">
        <h2>执行结果</h2>
        {% if has_html_result %}
            <div class="html-result-container">
                <!-- 使用iframe完全隔离HTML样式，保持原始外观 -->
                <iframe
                    srcdoc="{{ html_result_content|escapejs }}"
                    style="width: 100%; min-height: 600px; border: none; background: white;"
                    onload="this.style.height = Math.max(this.contentWindow.document.body.scrollHeight, 400) + 'px'">
                </iframe>
            </div>
        {% else %}
            <div class="empty-result-message">
                <p>📄 暂无HTML结果文件</p>
                <p class="help-text">脚本需要生成名为"结果正文.html"的文件来显示结果内容</p>
            </div>
        {% endif %}
    </fieldset>

    {% if record.stdout %}
    <fieldset class="module aligned">
        <h2>标准输出</h2>
        <pre class="output-content">{{ record.stdout }}</pre>
    </fieldset>
    {% endif %}

    {% if record.stderr %}
    <fieldset class="module aligned">
        <h2>错误输出</h2>
        <pre class="error-content">{{ record.stderr }}</pre>
    </fieldset>
    {% endif %}

    {% if output_files %}
    <fieldset class="module aligned">
        <h2>输出文件</h2>
        <ul class="output-files">
            {% for file in output_files %}
            {% if file.name %}
            <li>
                <a href="{% url 'script_manager:download_output_file' record.id file.name %}"
                   class="file-download">
                    {{ file.name }} ({{ file.size|filesizeformat }})
                </a>
            </li>
            {% endif %}
            {% endfor %}
        </ul>
    </fieldset>
    {% endif %}

    {% if stats %}
    <fieldset class="module aligned">
        <h2>脚本统计</h2>
        <div class="form-row">
            <div class="field-box">
                <label>总执行次数:</label>
                <p>{{ stats.total }}</p>
            </div>
        </div>
        <div class="form-row">
            <div class="field-box">
                <label>成功次数:</label>
                <p>{{ stats.success }}</p>
            </div>
        </div>
        <div class="form-row">
            <div class="field-box">
                <label>失败次数:</label>
                <p>{{ stats.failed }}</p>
            </div>
        </div>
        <div class="form-row">
            <div class="field-box">
                <label>成功率:</label>
                <p>{{ stats.success_rate|floatformat:2 }}%</p>
            </div>
        </div>
    </fieldset>
    {% endif %}

    <div class="submit-row">
        <a href="{% url 'admin:script_manager_executionrecord_changelist' %}" class="button">返回列表</a>
        {% if record.is_running %}
        <a href="{% url 'script_manager:cancel_execution' record.id %}"
           class="button" onclick="return confirm('确定要取消执行吗？')">取消执行</a>
        {% endif %}
    </div>
</div>

<style>
.output-content, .error-content {
    background: #f8f8f8;
    border: 1px solid #ddd;
    padding: 10px;
    max-height: 400px;
    overflow-y: auto;
    font-family: monospace;
    white-space: pre-wrap;
}

.error-content {
    background: #fff5f5;
    border-color: #f5c6cb;
    color: #721c24;
}

/* 统一的状态样式 */
.status-success {
    color: #28a745;
    font-weight: bold;
    background-color: #d4edda;
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid #c3e6cb;
}
.status-failed {
    color: #dc3545;
    font-weight: bold;
    background-color: #f8d7da;
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid #f5c6cb;
}
.status-running {
    color: #007bff;
    font-weight: bold;
    background-color: #d1ecf1;
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid #bee5eb;
}
.status-pending {
    color: #6c757d;
    font-weight: bold;
    background-color: #e2e3e5;
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid #d6d8db;
}
.status-cancelled {
    color: #6c757d;
    font-weight: bold;
    background-color: #e2e3e5;
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid #d6d8db;
}

.output-files {
    list-style: none;
    padding: 0;
}

.output-files li {
    margin: 5px 0;
}

/* 修复文件下载链接可见性 */
.file-download {
    display: inline-block;
    padding: 8px 12px;
    background: #28a745;
    color: white !important;
    text-decoration: none;
    border-radius: 4px;
    font-weight: 500;
    border: 1px solid #28a745;
    transition: all 0.2s ease;
}

.file-download:hover {
    background: #218838;
    color: white !important;
    text-decoration: none;
    border-color: #1e7e34;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.file-download:visited {
    color: white !important;
}

.file-download:active {
    color: white !important;
}

/* HTML结果显示样式 - 保持原始样式不被覆盖 */
.html-result-container {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    background: #fff;
    max-height: 600px;
    overflow-y: auto;
    margin: 10px 0;
    /* 重要：保持HTML内容的原始样式 */
    color: inherit;
}

/* 保持HTML内容的原始样式，特别是颜色和背景 */
.html-result-container iframe,
.html-result-container div,
.html-result-container span,
.html-result-container p,
.html-result-container h1,
.html-result-container h2,
.html-result-container h3,
.html-result-container h4,
.html-result-container h5,
.html-result-container h6 {
    color: inherit;
    background-color: inherit;
}

/* 特别保护绿色背景不被覆盖 */
.html-result-container [style*="background-color: #5cb85c"],
.html-result-container [style*="background-color: #28a745"],
.html-result-container [style*="background-color: rgb(92, 184, 92)"],
.html-result-container [style*="background-color: rgb(40, 167, 69)"],
.html-result-container [style*="background: #5cb85c"],
.html-result-container [style*="background: #28a745"] {
    background-color: inherit !important;
}

/* 但是保留表格的基本结构样式 */
.html-result-container table {
    border-collapse: collapse !important;
    margin: 10px 0 !important;
}

.html-result-container table th,
.html-result-container table td {
    border: 1px solid #ddd !important;
    padding: 8px !important;
    text-align: left !important;
}

/* 移除对表格头部背景色的覆盖，让HTML内容自己的样式生效 */
.html-result-container table th {
    font-weight: bold !important;
    /* 移除background-color覆盖，让原始HTML样式生效 */
}

.empty-result-message {
    text-align: center;
    padding: 40px 20px;
    background: #f9f9f9;
    border: 1px dashed #ccc;
    border-radius: 4px;
    color: #666;
}

.empty-result-message p {
    margin: 10px 0;
}

.empty-result-message .help-text {
    font-size: 0.9em;
    color: #999;
}


</style>
{% endblock %}
