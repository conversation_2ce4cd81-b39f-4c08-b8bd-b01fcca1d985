{% extends "admin/base_site.html" %}
{% load i18n admin_urls static %}

{% block title %}执行结果: {{ record.execution_id }}{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
    <a href="{% url 'admin:index' %}">首页</a>
    &rsaquo; <a href="{% url 'admin:script_manager_executionrecord_changelist' %}">执行记录</a>
    &rsaquo; 执行结果: {{ record.execution_id }}
</div>
{% endblock %}

{% block content %}
<div class="module aligned">
    <h1>执行结果: {{ record.execution_id }}</h1>

    <!-- 1. 执行结果区域 - 移到最前面 -->
    <fieldset class="module aligned">
        <h2>🎯 执行结果</h2>
        {% if has_html_result %}
            <div class="html-result-container">
                <!-- 使用iframe完全隔离HTML样式，保持原始外观 -->
                <iframe
                    srcdoc="{{ html_result_content|escapejs }}"
                    style="width: 100%; min-height: 600px; border: none; background: white;"
                    onload="this.style.height = Math.max(this.contentWindow.document.body.scrollHeight, 400) + 'px'">
                </iframe>
            </div>
        {% else %}
            <div class="empty-result-message">
                <p>📄 暂无HTML结果文件</p>
                <p class="help-text">脚本需要生成名为"结果正文.html"的文件来显示结果内容</p>
            </div>
        {% endif %}
    </fieldset>

    <!-- 2. 基本信息区域 - 移到第二位 -->
    <fieldset class="module aligned">
        <h2>📋 基本信息</h2>
        <div class="form-row">
            <div class="field-box">
                <label>脚本名称:</label>
                <p>{{ record.script.name }}</p>
            </div>
        </div>
        <div class="form-row">
            <div class="field-box">
                <label>执行状态:</label>
                <p class="status-{{ record.status }}">
                    {% if record.status == 'success' %}✓ 成功
                    {% elif record.status == 'failed' %}✗ 失败
                    {% elif record.status == 'running' %}⏳ 运行中
                    {% elif record.status == 'pending' %}⏸ 等待中
                    {% elif record.status == 'cancelled' %}⏹ 已取消
                    {% endif %}
                </p>
            </div>
        </div>
        <div class="form-row">
            <div class="field-box">
                <label>触发方式:</label>
                <p>{{ record.get_trigger_type_display }}</p>
            </div>
        </div>
        {% if record.triggered_by %}
        <div class="form-row">
            <div class="field-box">
                <label>触发者:</label>
                <p>{{ record.triggered_by.username }}</p>
            </div>
        </div>
        {% endif %}
        <div class="form-row">
            <div class="field-box">
                <label>开始时间:</label>
                <p>{{ record.started_at|default:"未开始" }}</p>
            </div>
        </div>
        <div class="form-row">
            <div class="field-box">
                <label>结束时间:</label>
                <p>{{ record.finished_at|default:"未结束" }}</p>
            </div>
        </div>
        {% if record.duration %}
        <div class="form-row">
            <div class="field-box">
                <label>执行时长:</label>
                <p>{{ record.duration }}</p>
            </div>
        </div>
        {% endif %}
        {% if record.return_code is not None %}
        <div class="form-row">
            <div class="field-box">
                <label>返回码:</label>
                <p>{{ record.return_code }}</p>
            </div>
        </div>
        {% endif %}
    </fieldset>

    <!-- 3. 执行参数区域 -->
    {% if record.parameters %}
    <fieldset class="module aligned">
        <h2>⚙️ 执行参数</h2>
        <pre>{{ record.parameters|pprint }}</pre>
    </fieldset>
    {% endif %}

    <!-- 4. 输出文件区域 - 提前显示，重要性高 -->
    {% if output_files %}
    <fieldset class="module aligned">
        <h2>📁 输出文件</h2>
        <div class="output-files-container">
            {% for file in output_files %}
            {% if file.name %}
            <div class="file-item">
                <a href="{% url 'script_manager:download_output_file' record.id file.name %}"
                   class="file-download-btn">
                    <i class="fas fa-download"></i>
                    {{ file.name }}
                    <span class="file-size">({{ file.size|filesizeformat }})</span>
                </a>
            </div>
            {% endif %}
            {% endfor %}
        </div>
    </fieldset>
    {% endif %}

    <!-- 5. 标准输出区域 -->
    {% if record.stdout %}
    <fieldset class="module aligned">
        <h2>📤 标准输出</h2>
        <pre class="output-content">{{ record.stdout }}</pre>
    </fieldset>
    {% endif %}

    <!-- 6. 错误输出区域 -->
    {% if record.stderr %}
    <fieldset class="module aligned">
        <h2>❌ 错误输出</h2>
        <pre class="error-content">{{ record.stderr }}</pre>
    </fieldset>
    {% endif %}

    <!-- 7. 脚本统计区域 -->
    {% if stats %}
    <fieldset class="module aligned">
        <h2>📊 脚本统计</h2>
        <div class="stats-grid">
            <div class="stat-item">
                <div class="stat-icon">📈</div>
                <div class="stat-content">
                    <label>总执行次数:</label>
                    <p class="stat-value">{{ stats.total }}</p>
                </div>
            </div>
            <div class="stat-item">
                <div class="stat-icon">✅</div>
                <div class="stat-content">
                    <label>成功次数:</label>
                    <p class="stat-value success">{{ stats.success }}</p>
                </div>
            </div>
            <div class="stat-item">
                <div class="stat-icon">❌</div>
                <div class="stat-content">
                    <label>失败次数:</label>
                    <p class="stat-value failed">{{ stats.failed }}</p>
                </div>
            </div>
            <div class="stat-item">
                <div class="stat-icon">📊</div>
                <div class="stat-content">
                    <label>成功率:</label>
                    <p class="stat-value rate">{{ stats.success_rate|floatformat:2 }}%</p>
                </div>
            </div>
        </div>
    </fieldset>
    {% endif %}

    <div class="submit-row">
        <a href="{% url 'admin:script_manager_executionrecord_changelist' %}" class="button">返回列表</a>
        {% if record.is_running %}
        <a href="{% url 'script_manager:cancel_execution' record.id %}"
           class="button" onclick="return confirm('确定要取消执行吗？')">取消执行</a>
        {% endif %}
    </div>
</div>

<style>
.output-content, .error-content {
    background: #f8f8f8;
    border: 1px solid #ddd;
    padding: 10px;
    max-height: 400px;
    overflow-y: auto;
    font-family: monospace;
    white-space: pre-wrap;
}

.error-content {
    background: #fff5f5;
    border-color: #f5c6cb;
    color: #721c24;
}

/* 统一的状态样式 */
.status-success {
    color: #28a745;
    font-weight: bold;
    background-color: #d4edda;
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid #c3e6cb;
}
.status-failed {
    color: #dc3545;
    font-weight: bold;
    background-color: #f8d7da;
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid #f5c6cb;
}
.status-running {
    color: #007bff;
    font-weight: bold;
    background-color: #d1ecf1;
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid #bee5eb;
}
.status-pending {
    color: #6c757d;
    font-weight: bold;
    background-color: #e2e3e5;
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid #d6d8db;
}
.status-cancelled {
    color: #6c757d;
    font-weight: bold;
    background-color: #e2e3e5;
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid #d6d8db;
}

/* 输出文件容器样式 - 兼容SimpleUI */
.output-files-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 15px;
    margin: 15px 0;
}

.file-item {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 5px;
    transition: all 0.2s ease;
}

.file-item:hover {
    background: #e9ecef;
    border-color: #28a745;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* 增强的文件下载按钮样式 - 确保在SimpleUI中可见 */
.file-download-btn {
    display: flex !important;
    align-items: center;
    padding: 12px 16px !important;
    background: linear-gradient(135deg, #28a745, #20c997) !important;
    color: white !important;
    text-decoration: none !important;
    border-radius: 6px !important;
    font-weight: 600 !important;
    border: none !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3) !important;
    width: 100%;
    justify-content: flex-start;
    gap: 8px;
}

.file-download-btn:hover {
    background: linear-gradient(135deg, #218838, #1ea080) !important;
    color: white !important;
    text-decoration: none !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4) !important;
}

.file-download-btn:visited,
.file-download-btn:active,
.file-download-btn:focus {
    color: white !important;
    text-decoration: none !important;
}

.file-download-btn i {
    font-size: 16px;
    margin-right: 8px;
}

.file-size {
    margin-left: auto;
    font-size: 0.9em;
    opacity: 0.8;
    background: rgba(255,255,255,0.2);
    padding: 2px 6px;
    border-radius: 3px;
}

/* HTML结果显示样式 - 增强SimpleUI兼容性 */
.html-result-container {
    border: 2px solid #28a745 !important;
    border-radius: 8px !important;
    padding: 0 !important;
    background: #fff !important;
    margin: 15px 0 !important;
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.1) !important;
    overflow: hidden !important;
}

.html-result-container iframe {
    width: 100% !important;
    min-height: 600px !important;
    border: none !important;
    background: white !important;
    display: block !important;
}

/* 保持HTML内容的原始样式，特别是颜色和背景 */
.html-result-container iframe,
.html-result-container div,
.html-result-container span,
.html-result-container p,
.html-result-container h1,
.html-result-container h2,
.html-result-container h3,
.html-result-container h4,
.html-result-container h5,
.html-result-container h6 {
    color: inherit;
    background-color: inherit;
}

/* 特别保护绿色背景不被覆盖 */
.html-result-container [style*="background-color: #5cb85c"],
.html-result-container [style*="background-color: #28a745"],
.html-result-container [style*="background-color: rgb(92, 184, 92)"],
.html-result-container [style*="background-color: rgb(40, 167, 69)"],
.html-result-container [style*="background: #5cb85c"],
.html-result-container [style*="background: #28a745"] {
    background-color: inherit !important;
}

/* 但是保留表格的基本结构样式 */
.html-result-container table {
    border-collapse: collapse !important;
    margin: 10px 0 !important;
}

.html-result-container table th,
.html-result-container table td {
    border: 1px solid #ddd !important;
    padding: 8px !important;
    text-align: left !important;
}

/* 移除对表格头部背景色的覆盖，让HTML内容自己的样式生效 */
.html-result-container table th {
    font-weight: bold !important;
    /* 移除background-color覆盖，让原始HTML样式生效 */
}

.empty-result-message {
    text-align: center;
    padding: 40px 20px;
    background: #f9f9f9;
    border: 1px dashed #ccc;
    border-radius: 4px;
    color: #666;
}

.empty-result-message p {
    margin: 10px 0;
}

.empty-result-message .help-text {
    font-size: 0.9em;
    color: #999;
}

/* 统计网格样式 - 现代化设计 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin: 20px 0;
}

.stat-item {
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border: 1px solid #dee2e6;
    border-radius: 10px;
    padding: 15px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    border-color: #28a745;
}

.stat-icon {
    font-size: 24px;
    margin-right: 12px;
    width: 40px;
    text-align: center;
}

.stat-content {
    flex: 1;
}

.stat-content label {
    font-size: 0.9em;
    color: #6c757d;
    margin: 0;
    display: block;
}

.stat-value {
    font-size: 1.5em;
    font-weight: bold;
    margin: 5px 0 0 0;
    color: #495057;
}

.stat-value.success {
    color: #28a745;
}

.stat-value.failed {
    color: #dc3545;
}

.stat-value.rate {
    color: #007bff;
}

/* 增强的空结果消息样式 */
.empty-result-message {
    text-align: center;
    padding: 40px 20px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border: 2px dashed #dee2e6;
    border-radius: 12px;
    color: #6c757d;
    margin: 20px 0;
}

.empty-result-message p:first-child {
    font-size: 1.2em;
    margin-bottom: 10px;
}


</style>
{% endblock %}
