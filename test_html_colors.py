#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试HTML颜色显示的脚本
"""

import os
import sys
from datetime import datetime


def main():
    print("🎨 开始生成HTML颜色测试文件")
    
    # 获取输出目录
    output_dir = os.environ.get('SCRIPT_OUTPUT_DIR', './output')
    execution_id = os.environ.get('EXECUTION_ID', 'test')
    
    print(f"📁 输出目录: {output_dir}")
    print(f"🆔 执行ID: {execution_id}")
    
    try:
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        # 生成与邮件中相同的HTML内容
        html_content = """
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <title>核心统计指标</title>
            <style>
                body {
                    font-family: 'Microsoft YaHei', Arial, sans-serif;
                    margin: 20px;
                    background-color: #f8f9fa;
                }
                .container {
                    background: white;
                    padding: 20px;
                    border-radius: 8px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }
                h1, h2 {
                    color: #333;
                    border-bottom: 2px solid #5cb85c;
                    padding-bottom: 10px;
                }
                .stats-grid {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 20px;
                    margin: 20px 0;
                }
                .stat-item {
                    background-color: #5cb85c;
                    color: white;
                    padding: 15px;
                    border-radius: 5px;
                    text-align: center;
                    font-weight: bold;
                }
                .stat-value {
                    font-size: 24px;
                    margin: 10px 0;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin: 20px 0;
                }
                th {
                    background-color: #5cb85c;
                    color: white;
                    padding: 12px;
                    text-align: left;
                    font-weight: bold;
                }
                td {
                    padding: 10px;
                    border: 1px solid #ddd;
                    background-color: #f9f9f9;
                }
                tr:nth-child(even) td {
                    background-color: #f1f1f1;
                }
                .highlight {
                    background-color: #5cb85c !important;
                    color: white !important;
                    font-weight: bold;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>核心统计指标</h1>
                
                <div class="stats-grid">
                    <div class="stat-item">
                        <div>总设备数</div>
                        <div class="stat-value">2</div>
                    </div>
                    <div class="stat-item">
                        <div>成功采集数</div>
                        <div class="stat-value">2</div>
                    </div>
                    <div class="stat-item">
                        <div>SNMP失败数</div>
                        <div class="stat-value">0</div>
                    </div>
                    <div class="stat-item">
                        <div>总模块数</div>
                        <div class="stat-value">38</div>
                    </div>
                    <div class="stat-item">
                        <div>在用模块数</div>
                        <div class="stat-value">24</div>
                    </div>
                    <div class="stat-item">
                        <div>空闲模块数</div>
                        <div class="stat-value">14</div>
                    </div>
                </div>
                
                <h2>全局空闲模块统计</h2>
                
                <table>
                    <thead>
                        <tr>
                            <th>模块型号</th>
                            <th>波长(nm)</th>
                            <th>数量</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>10GBASE_LR</td>
                            <td>1310</td>
                            <td class="highlight">14</td>
                        </tr>
                    </tbody>
                </table>
                
                <div style="margin-top: 30px; padding: 15px; background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px;">
                    <strong>✅ 测试说明：</strong> 这个HTML文件测试绿色背景是否能正确显示。
                    如果您看到鲜艳的绿色（#5cb85c），说明样式修复成功！
                </div>
                
                <div style="margin-top: 15px; text-align: center; color: #666;">
                    <small>生成时间: """ + datetime.now().strftime('%Y-%m-%d %H:%M:%S') + """</small>
                </div>
            </div>
        </body>
        </html>
        """
        
        # 保存HTML文件
        html_file = os.path.join(output_dir, '结果正文.html')
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        # 生成一些测试文件
        test_files = [
            ('Optical_Report_20250529.csv', 'CSV报告文件内容...'),
            ('Idle_Modules_Report_20250529.csv', '空闲模块报告内容...'),
            ('Summary_Report.txt', '总结报告文本内容...')
        ]
        
        for filename, content in test_files:
            file_path = os.path.join(output_dir, filename)
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
        
        print(f"✅ HTML测试文件已生成: {html_file}")
        print(f"✅ 测试文件已生成: {len(test_files)} 个")
        print("🎨 请查看执行结果页面，验证以下功能:")
        print("   1. 绿色背景是否显示正确")
        print("   2. 文件下载按钮是否正常工作")
        print("   3. 页面布局是否按新顺序显示")
        
        return 0
        
    except Exception as e:
        print(f"❌ 生成失败: {str(e)}", file=sys.stderr)
        return 1


if __name__ == '__main__':
    sys.exit(main())
