import os
from celery import Celery
from django.conf import settings

# 设置Django设置模块
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'script_platform.settings')

app = Celery('script_platform')

# 使用Django的设置文件配置Celery
app.config_from_object('django.conf:settings', namespace='CELERY')

# 自动发现任务
app.autodiscover_tasks()

# 定时任务配置
app.conf.beat_schedule = {
    'cleanup-old-execution-records': {
        'task': 'script_manager.tasks.cleanup_old_execution_records',
        'schedule': 86400.0,  # 每天执行一次
    },
    'cleanup-old-system-logs': {
        'task': 'script_manager.tasks.cleanup_old_system_logs',
        'schedule': 86400.0,  # 每天执行一次
    },
}

app.conf.timezone = 'Asia/Shanghai'

@app.task(bind=True)
def debug_task(self):
    print(f'Request: {self.request!r}')
