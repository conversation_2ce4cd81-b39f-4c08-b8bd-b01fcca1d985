from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.http import HttpResponseRedirect
from django.contrib import messages
from .models import Script, ScriptParameter, ScheduledTask, ExecutionRecord, SystemLog


class ScriptParameterInline(admin.TabularInline):
    """脚本参数内联编辑"""
    model = ScriptParameter
    extra = 1
    fields = ['name', 'display_name', 'parameter_type', 'is_required', 'default_value', 'order']


@admin.register(Script)
class ScriptAdmin(admin.ModelAdmin):
    list_display = ['name', 'owner', 'is_active', 'created_at', 'updated_at', 'execute_button']
    list_filter = ['is_active', 'owner', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at', 'updated_at']
    inlines = [ScriptParameterInline]

    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'description', 'file', 'owner', 'is_active')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def execute_button(self, obj):
        """执行按钮"""
        if obj.pk:
            url = reverse('admin:execute_script', args=[obj.pk])
            return format_html('<a class="button" href="{}">立即执行</a>', url)
        return '-'
    execute_button.short_description = '操作'

    def save_model(self, request, obj, form, change):
        if not change:  # 新建时设置owner为当前用户
            obj.owner = request.user
        super().save_model(request, obj, form, change)


@admin.register(ScriptParameter)
class ScriptParameterAdmin(admin.ModelAdmin):
    list_display = ['script', 'display_name', 'parameter_type', 'is_required', 'order']
    list_filter = ['parameter_type', 'is_required']
    search_fields = ['script__name', 'display_name', 'name']
    ordering = ['script', 'order']


@admin.register(ScheduledTask)
class ScheduledTaskAdmin(admin.ModelAdmin):
    list_display = ['name', 'script', 'cron_expression', 'is_active', 'last_run_at', 'next_run_at']
    list_filter = ['is_active', 'script', 'created_at']
    search_fields = ['name', 'script__name']
    readonly_fields = ['created_by', 'created_at', 'last_run_at', 'next_run_at']

    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'script', 'cron_expression', 'is_active')
        }),
        ('执行参数', {
            'fields': ('parameters',),
            'description': '以JSON格式输入执行参数'
        }),
        ('时间信息', {
            'fields': ('created_by', 'created_at', 'last_run_at', 'next_run_at'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        if not change:  # 新建时设置创建者为当前用户
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(ExecutionRecord)
class ExecutionRecordAdmin(admin.ModelAdmin):
    list_display = ['execution_id', 'script', 'status', 'trigger_type', 'triggered_by', 'created_at', 'duration_display', 'view_result']
    list_filter = ['status', 'trigger_type', 'script', 'created_at']
    search_fields = ['execution_id', 'script__name']
    readonly_fields = ['execution_id', 'created_at', 'started_at', 'finished_at', 'duration_display']

    fieldsets = (
        ('基本信息', {
            'fields': ('execution_id', 'script', 'scheduled_task', 'status', 'trigger_type', 'triggered_by')
        }),
        ('执行参数', {
            'fields': ('parameters',),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'started_at', 'finished_at', 'duration_display'),
            'classes': ('collapse',)
        }),
        ('执行结果', {
            'fields': ('return_code', 'stdout', 'stderr', 'output_files'),
            'classes': ('collapse',)
        }),
    )

    def duration_display(self, obj):
        """显示执行时长"""
        duration = obj.duration
        if duration:
            total_seconds = int(duration.total_seconds())
            hours, remainder = divmod(total_seconds, 3600)
            minutes, seconds = divmod(remainder, 60)
            return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        return '-'
    duration_display.short_description = '执行时长'

    def view_result(self, obj):
        """查看结果按钮"""
        if obj.pk:
            url = reverse('admin:view_execution_result', args=[obj.pk])
            return format_html('<a class="button" href="{}">查看结果</a>', url)
        return '-'
    view_result.short_description = '操作'

    def has_add_permission(self, request):
        """禁止手动添加执行记录"""
        return False


@admin.register(SystemLog)
class SystemLogAdmin(admin.ModelAdmin):
    list_display = ['level', 'message_short', 'module', 'user', 'created_at']
    list_filter = ['level', 'module', 'created_at']
    search_fields = ['message', 'module']
    readonly_fields = ['created_at']

    def message_short(self, obj):
        """显示消息摘要"""
        return obj.message[:100] + '...' if len(obj.message) > 100 else obj.message
    message_short.short_description = '消息'

    def has_add_permission(self, request):
        """禁止手动添加系统日志"""
        return False
