﻿#! /usr/bin/env python
# -*- coding:utf-8 -*-
# Author:helj

import paramiko, time, datetime, xlwt, os, smtplib, re
import pandas  as pd
import email.mime.text
import email.mime.multipart
from email.mime.multipart import MI<PERSON>Multipart
from email.mime.text import MIMEText
from email.mime.application import MIMEApplication

FiveNine_dict = dict()
FiveNineOut_dict = dict()
Month_Max_dict = dict()
Mail_Title = dict()


def Main_excel_creat(Today_Name, Yesterday_Name, today_star_int, today_end_int, Yesterday_star_int, Yesterday_end_int,
                     Month_star_int):
    alignment1 = xlwt.Alignment()  # 新建框架
    alignment1.horz = xlwt.Alignment.HORZ_CENTER  # 行居中
    alignment1.vert = xlwt.Alignment.VERT_CENTER  # 列居中
    font1 = xlwt.Font()  # 新建字体
    font1.bold = False  # 字体加粗
    font1.height = 12 * 20  # 12号的字体,
    font1.name = "宋体"
    pattern1 = xlwt.Pattern()  # 设置背景色
    pattern1.pattern = xlwt.Pattern.SOLID_PATTERN  # 设置背景色模式
    pattern1.pattern_fore_colour = 1  # 0：黑  1：白  2：红  3：绿  4：蓝  5：黄  6：紫  7：青
    borders1 = xlwt.Borders()  # 设置单元格边框
    borders1.left = xlwt.Borders.THIN  # THIN所表示的值为1，边框为实线
    borders1.right = xlwt.Borders.THIN
    borders1.top = xlwt.Borders.THIN
    borders1.bottom = xlwt.Borders.THIN
    style1 = xlwt.XFStyle()
    style1.alignment = alignment1
    style1.font = font1
    style1.pattern = pattern1
    style1.borders = borders1

    font2 = xlwt.Font()  # 新建字体
    font2.bold = False  # 字体加粗
    font2.height = 12 * 20  # 12号的字体,
    font2.name = "宋体"
    pattern2 = xlwt.Pattern()  # 设置背景色
    pattern2.pattern = xlwt.Pattern.SOLID_PATTERN  # 设置背景色模式
    pattern2.pattern_fore_colour = 2  # 0：黑  1：白  2：红  3：绿  4：蓝  5：黄  6：紫  7：青
    style2 = xlwt.XFStyle()
    style2.alignment = alignment1
    style2.font = font2
    style2.pattern = pattern2
    style2.borders = borders1

    workbook = xlwt.Workbook(encoding='utf-8')
    worksheet = workbook.add_sheet('sheet1')  # 新建sheet
    worksheet.write(0, 0, '客户名称', style1)
    worksheet.write(0, 1, '%s(M)' % Today_Name, style1)
    worksheet.write(0, 2, '%s(M)' % Yesterday_Name, style1)
    worksheet.write(0, 3, '削峰前95值M（当月至今）', style1)
    # worksheet.write(0, 3, '月峰值M', style1)
    worksheet.write(0, 4, '95值/峰值', style1)
    worksheet.write(0, 5, 'in减out值', style1)
    worksheet.write(0, 6, '95值M（当月至今）', style1)
    worksheet.write(0, 7, 'out95值(同左)', style1)
    worksheet.write(0, 8, '95-95out(同左)', style1)
    worksheet.write(0, 9, '削减流量', style1)
    worksheet.write(0, 10, '削减百分百', style1)
    global ALL_client_list

    ALL_client_list = [
        ['佛山电信 FS-Z-S8861-01 25GE1/1/1', '5000', '461/24326'],
        ['佛山电信 FS-Z-S8861-01 25GE1/1/2', '5000', '461/24327'],
        ['佛山电信 FS-Z-S8861-01 25GE1/1/3', '5000', '461/24328'],
        ['佛山电信 FS-Z-S8861-01 25GE1/1/4', '5000', '461/24329'],
        ['佛山电信 FS-Z-S8861-01 25GE1/1/5', '5000', '461/24330'],
        ['佛山电信 FS-Z-S8861-01 25GE1/1/6', '5000', '461/24331'],
        ['佛山电信 FS-Z-S8861-01 25GE1/1/7', '5000', '461/24332'],
        # ['佛山电信 FS-Z-S8861-01 25GE1/1/8', '5000', '461/24333'],
        # ['佛山电信 FS-Z-S8861-01 25GE1/1/9', '5000', '461/24334'],
        # ['佛山电信 FS-Z-S8861-01 25GE1/1/10', '5000', '461/24335'],
        # ['佛山电信 FS-Z-S8861-01 25GE1/1/21', '5000', '461/24346'],
        # ['佛山电信 FS-Z-S8861-01 25GE1/1/22', '5000', '461/24347'],
#        ['佛山电信 FS-Z-S8861-01 25GE1/4/13', '5000', '461/24402'],
#         ['佛山电信 FS-Z-S8861-01 25GE1/4/14', '5000', '461/24403'],
        # ['佛山电信 FS-Z-S8861-01 25GE1/4/15', '5000', '461/24404'],
        ['佛山电信 FS-Z-S8861-01 25GE1/4/16', '5000', '461/24405'],
        ['佛山电信 FS-Z-S8861-01 25GE1/4/17', '5000', '461/24406'],
        ['佛山电信 FS-Z-S8861-01 25GE1/4/18', '5000', '461/24407'],
        # ['佛山电信 FS-Z-S8861-01 25GE1/4/19', '5000', '461/24408'],
        # ['佛山电信 FS-Z-S8861-01 25GE1/4/20', '5000', '461/24409'],
        ['佛山电信 FS-Z-S8861-02 25GE1/1/1', '5000', '720/42073'],
        ['佛山电信 FS-Z-S8861-02 25GE1/1/2', '5000', '720/42074'],
        ['佛山电信 FS-Z-S8861-02 25GE1/1/3', '5000', '720/42075'],
        ['佛山电信 FS-Z-S8861-02 25GE1/1/4', '5000', '720/42076'],
        ['佛山电信 FS-Z-S8861-02 25GE1/1/5', '5000', '720/42077'],
        ['佛山电信 FS-Z-S8861-02 25GE1/1/6', '5000', '720/42078'],
        # ['佛山电信 FS-Z-S8861-02 25GE1/1/7', '5000', '720/42079'],
        # ['佛山电信 FS-Z-S8861-02 25GE1/1/8', '5000', '720/42080'],
        # ['佛山电信 FS-Z-S8861-02 25GE1/1/9', '5000', '720/42081'],
        # ['佛山电信 FS-Z-S8861-02 25GE1/1/10', '5000', '720/42082'],
        # ['佛山电信 FS-Z-S8861-02 25GE1/1/21', '5000', '720/42083'],
        # ['佛山电信 FS-Z-S8861-02 25GE1/1/22', '5000', '720/42084'],
        # ['佛山电信 FS-Z-S8861-02 25GE1/4/13', '5000', '720/42124'],
       # ['佛山电信 FS-Z-S8861-02 25GE1/4/14', '5000', '720/42125'],
        ['佛山电信 FS-Z-S8861-02 25GE1/4/15', '5000', '720/42126'],
        ['佛山电信 FS-Z-S8861-02 25GE1/4/16', '5000', '720/42127'],
        ['佛山电信 FS-Z-S8861-02 25GE1/4/17', '5000', '720/42128'],
        ['佛山电信 FS-Z-S8861-02 25GE1/4/18', '5000', '720/42129'],
        # ['佛山电信 FS-Z-S8861-02 25GE1/4/19', '5000', '720/42130'],
        # ['佛山电信 FS-Z-S8861-02 25GE1/4/20', '5000', '720/42131'],

        ['佛山电信削峰组', '300000', '461/24326', '461/24327', '461/24328', '461/24329', '461/24330', '461/24331', '461/24332',
         '461/24334', '461/24402', '461/24404', '461/24405', '461/24406', '461/24407', '461/24408',
         '720/42073', '720/42074', '720/42075', '720/42076', '720/42077', '720/42078', '720/42079', '720/42080',
         '720/42124', '720/42126', '720/42127', '720/42128', '720/42129', '720/42130'],  # '461/24403', '720/42125'

#         ['广州电信 LY-Z-S8861-01 25GE1/1/1', '3000', '452/23574'],
        ['广州电信 LY-Z-S8861-01 25GE1/1/2', '3000', '452/23575'],
        ['广州电信 LY-Z-S8861-01 25GE1/1/3', '3000', '452/23576'],
        ['广州电信 LY-Z-S8861-01 25GE1/1/4', '3000', '452/23577'],
        ['广州电信 LY-Z-S8861-01 25GE1/1/5', '3000', '452/23578'],
#         ['广州电信 LY-Z-S8861-01 25GE1/1/6', '3000', '452/23579'],
#         ['广州电信 LY-Z-S8861-01 25GE1/1/7', '3000', '452/23580'],
#         ['广州电信 LY-Z-S8861-01 25GE1/1/8', '3000', '452/23581'],
#         ['广州电信 LY-Z-S8861-01 25GE1/1/9', '3000', '452/23582'],
#         ['广州电信 LY-Z-S8861-01 25GE1/1/10', '3000', '452/23583'],
        ['广州电信 LY-Z-S8861-01 100GE1/2/3', '30000', '452/23602'],
#
#         ['广州电信 LY-Z-S8861-02 25GE1/1/1', '3000', '451/23499'],
        ['广州电信 LY-Z-S8861-02 25GE1/1/2', '3000', '451/23500'],
        ['广州电信 LY-Z-S8861-02 25GE1/1/3', '3000', '451/23501'],
#         ['广州电信 LY-Z-S8861-02 25GE1/1/4', '3000', '451/23502'],
        ['广州电信 LY-Z-S8861-02 25GE1/1/5', '3000', '451/23503'],
#         ['广州电信 LY-Z-S8861-02 25GE1/1/6', '3000', '451/23504'],
#         ['广州电信 LY-Z-S8861-02 25GE1/1/7', '3000', '451/23505'],
#         ['广州电信 LY-Z-S8861-02 25GE1/1/8', '3000', '451/23506'],
#         ['广州电信 LY-Z-S8861-02 25GE1/1/9', '3000', '451/23507'],
#         ['广州电信 LY-Z-S8861-02 25GE1/1/10', '3000', '451/23508'],
        ['广州电信 LY-Z-S8861-02 25GE1/1/12', '3000', '451/23510'],
        ['广州电信 LY-Z-S8861-02 100GE1/2/3', '30000', '451/23527'],
#
#         ['广州电信 LY-X-S6800-03 T1/0/42', '3000', '495/26586'],
#         ['广州电信 LY-X-S6800-03 T1/0/43', '3000', '495/26587'],
#         ['广州电信 LY-X-S6800-03 T1/0/45', '3000', '495/26589'],
#         ['广州电信 LY-X-S6800-03 T1/0/46', '3000', '495/26590'],
#         ['广州电信 LY-X-S6800-03 T1/0/47', '3000', '495/26591'],
#         ['广州电信 LY-X-S6800-03 T1/0/48', '3000', '495/26592'],
#
#         ['广州电信 LY-X-S6800-04 T1/0/42', '3000', '494/26520'],
#         ['广州电信 LY-X-S6800-04 T1/0/43', '3000', '494/26521'],
# #        ['广州电信 LY-X-S6800-04 T1/0/44', '3000', '494/26522'],
#         ['广州电信 LY-X-S6800-04 T1/0/45', '3000', '494/26523'],
#         ['广州电信 LY-X-S6800-04 T1/0/46', '3000', '494/26524'],
#         ['广州电信 LY-X-S6800-04 T1/0/47', '3000', '494/26525'],
#         ['广州电信 LY-X-S6800-04 T1/0/48', '3000', '494/26526'],
#         
        ['广州电信削峰组', '84000', '452/23575', '452/23576', '452/23577', '452/23578', '452/23602', '451/23500', '451/23501', '451/23503', '451/23510', '451/23527']
        # ['广州电信削峰组', '300000', '452/23574', '452/23575', '452/23576', '452/23577', '452/23578', '452/23579', '452/23580',
        #  '452/23581', '452/23582', '452/23583', '451/23499', '451/23500', '451/23501', '451/23502', '451/23503',
        #  '451/23504', '451/23505', '451/23506', '451/23507', '451/23508', '495/26586', '495/26587', '495/26589', '495/26590',
        #  '495/26591', '495/26592', '494/26520', '494/26521', '494/26522', '494/26523', '494/26524', '494/26525', '494/26526', '452/23602', '451/23510', '451/23527']
    ]

    Today_sum_list = []
    Yesterday_sum_list = []
    Purchase_sum_list = []
    Idle_bandwidth_sum_list = []
    Peak_num_sum_list = []
    Month_Idle_bandwidth_sum_list = []

    num = 1

    while num <= len(ALL_client_list):
        for ev_pro in ALL_client_list:
            #            print(ev_pro[0])
            Taday_commands = commands_creation(ev_pro[2:], today_star_int, today_end_int)
            today_inital_datas, Taday_Max_in, Taday_Max_out, Taday_Max_inMinusOut = Aggregate_command_reply_list(
                Taday_commands, len(Taday_commands))

            Yesterday_commands = commands_creation(ev_pro[2:], Yesterday_star_int, Yesterday_end_int)
            yesterday_inital_datas, Yesterday_Max_in, Yesterday_Max_out, Yesterday_Max_inMinusOut = Aggregate_command_reply_list(
                Yesterday_commands, len(Taday_commands))

            Month_commands = commands_creation(ev_pro[2:], Month_star_int, today_end_int)
            month_inital_datas, Month_Max_in, Month_Max_out, Month_Max_inMinusOut = Aggregate_command_reply_list(
                Month_commands, len(Taday_commands))

            if Taday_Max_in >= Taday_Max_out:
                Taday_Max = Taday_Max_in
            else:
                Taday_Max = Taday_Max_out
            Taday_Max = int(float(Taday_Max) * 8 / 1000 / 1000)
            Today_sum_list.append(Taday_Max)

            if Yesterday_Max_in >= Yesterday_Max_out:
                Yesterday_Max = Yesterday_Max_in
            else:
                Yesterday_Max = Yesterday_Max_out
            Yesterday_Max = int(float(Yesterday_Max) * 8 / 1000 / 1000)
            Yesterday_sum_list.append(Yesterday_Max)

            if Taday_Max - Yesterday_Max == 0:
                Change_Percentage = 0
            elif int(Yesterday_Max) == 0:
                Change_Percentage = 100
            else:
                Change_Percentage = (Taday_Max - Yesterday_Max) / Yesterday_Max * 100

            Purchase_num = int(ev_pro[1])
            Purchase_sum_list.append(Purchase_num)

            Utilization_rate = Taday_Max / Purchase_num * 100
            if Utilization_rate >= 100:
                Utilization_rate = 100
            else:
                Utilization_rate = Utilization_rate

            Idle_bandwidth = int(ev_pro[1]) - int(Taday_Max)
            if Idle_bandwidth <= 0:
                Idle_bandwidth = 0
            else:
                Idle_bandwidth = Idle_bandwidth
            Idle_bandwidth_sum_list.append(Idle_bandwidth)

            if Month_Max_in >= Month_Max_out:
                Month_Max = Month_Max_in
            else:
                Month_Max = Month_Max_out
            Month_Max = int(float(Month_Max) * 8 / 1000 / 1000)
            # Month_Max_dict[ev_pro[0]] = Month_Max
            Peak_num_sum_list.append(Month_Max)
            Month_Max_dict[ev_pro[0]] = Month_Max

            Month_Idle_bandwidth = int(ev_pro[1]) - int(Month_Max)
            if Month_Idle_bandwidth <= 0:
                Month_Idle_bandwidth = 0
            else:
                Month_Idle_bandwidth = Month_Idle_bandwidth
            Month_Idle_bandwidth_sum_list.append(Month_Idle_bandwidth)

            five_nine_value = five_nine_caculate(month_inital_datas[-1])
            FiveNine_dict[ev_pro[0]] = five_nine_value

            five_nine_out_value = five_nine_caculate_out(month_inital_datas[-1])
            FiveNineOut_dict[ev_pro[0]] = five_nine_out_value

            worksheet.write(num, 0, ev_pro[0], style1)
            worksheet.write(num, 1, Taday_Max, style1)
            worksheet.write(num, 2, Yesterday_Max, style1)
            if num <= 32:
                if num == 21:
                    FS_Cut_b = int(float(five_nine_value) * 8 / 1000 / 1000)
                    special_style = style1 if FS_Cut_b <= 100000 else style2  # 如果FS_Cut_b大于100000，则标红
                    worksheet.write(num, 3, FS_Cut_b, special_style)  # 削峰前汇总,
                if num == 32:
                    LY_Cut_b = int(float(five_nine_value) * 8 / 1000 / 1000)
                    special_style = style1 if LY_Cut_b <= 100000 else style2  # 如果LY_Cut_b大于100000，则标红
                    worksheet.write(num, 3, LY_Cut_b, special_style)  # 削峰前汇总
                if num != 21:
                    if num != 32:
                        worksheet.write(num, 6, int(float(five_nine_value) * 8 / 1000 / 1000), style1)
                        worksheet.write(num, 7, int(float(five_nine_out_value) * 8 / 1000 / 1000), style1)
                        worksheet.write(num, 8, int(float(five_nine_value) * 8 / 1000 / 1000) - int(
                            float(five_nine_out_value) * 8 / 1000 / 1000), style1)

                    #                worksheet.write(num, 3, Month_Max, style1)
                FiveNine_Max_rate = int(
                    float(five_nine_value) * 8 / 1000 / 1000) / Month_Max * 100 if Month_Max != 0 else 0
                worksheet.write(num, 4, '%.2f' % FiveNine_Max_rate + '%', style1)
                worksheet.write(num, 5, int(float(Month_Max_inMinusOut) * 8 / 1000 / 1000), style1)
            num += 1
    # #特殊添加
    # #计算佛山电信单独端口计费总和
    #
    FS_DX_sum, LY_DX_sum = 0, 0
    for item in FiveNine_dict:
        if '佛山电信' in item and '削峰' not in item:
            FS_DX_sum += FiveNine_dict[item]
        if '广州电信' in item and '削峰' not in item:
            LY_DX_sum += FiveNine_dict[item]
    #
    # #计算out95值总和
    FS_Out_sum, LY_Out_sum = 0, 0
    for item in FiveNineOut_dict:
        if '佛山电信' in item and '削峰' not in item:
            FS_Out_sum += FiveNineOut_dict[item]
        if '广州电信' in item and '削峰' not in item:
            LY_Out_sum += FiveNineOut_dict[item]
            #
            # #计算佛山差值以及差值百分比
    FS_Difference_value = FiveNine_dict['佛山电信削峰组'] - FS_DX_sum  # 合并计费值-单独计费值
    FS_Difference_rate = float((FS_Cut_b - FS_DX_sum * 8 / 1000 / 1000) / FS_Cut_b * 100) if FS_Cut_b != 0 else 0
    FS_in_affect = int(float(FS_DX_sum) * 8 / 1000 / 1000) - int(float(FS_Out_sum) * 8 / 1000 / 1000)
    #
    Mail_Title['FS_Difference_value'] = int(FS_Difference_value * 8 / 1000 / 1000)
    Mail_Title['FS_Difference_rate'] = FS_Difference_rate
    Mail_Title['FS_in_affect'] = FS_in_affect
    #
    #
    worksheet.write(21, 6, int(float(FS_DX_sum) * 8 / 1000 / 1000), style1)
    worksheet.write(21, 7, int(float(FS_Out_sum) * 8 / 1000 / 1000), style1)
    worksheet.write(21, 8, FS_in_affect, style1)
    worksheet.write(21, 9, int(float(FS_Difference_value) * 8 / 1000 / 1000), style1)
    worksheet.write(21, 10, '%0.2f' % FS_Difference_rate + '%', style1)

    # # #计算广州差值以及差值百分比
    LY_Difference_value = FiveNine_dict['广州电信削峰组'] - LY_DX_sum  # 合并计费值-单独计费值
    LY_Difference_rate = float((LY_Cut_b - LY_DX_sum * 8 / 1000 / 1000) / LY_Cut_b * 100) if LY_Cut_b != 0 else 0
    LY_in_affect = int(float(LY_DX_sum) * 8 / 1000 / 1000) - int(float(LY_Out_sum) * 8 / 1000 / 1000)
    
    Mail_Title['LY_Difference_value'] = int(LY_Difference_value * 8 / 1000 / 1000)
    Mail_Title['LY_Difference_rate'] = LY_Difference_rate
    Mail_Title['LY_in_affect'] = LY_in_affect

    worksheet.write(32, 6, int(float(LY_DX_sum) * 8 / 1000 / 1000), style1)
    worksheet.write(32, 7, int(float(LY_Out_sum) * 8 / 1000 / 1000), style1)
    worksheet.write(32, 8, int(float(LY_DX_sum) * 8 / 1000 / 1000) - int(float(LY_Out_sum) * 8 / 1000 / 1000), style1)
    worksheet.write(32, 9, int(float(LY_Difference_value) * 8 / 1000 / 1000), style1)
    worksheet.write(32, 10, '%0.2f' % LY_Difference_rate + '%', style1)

    # # # #计算广州佛山总和
    # FS_LY_cut_b = FS_Cut_b + LY_Cut_b  # 佛山广州削峰前总和
    # FS_LY_Month_max_sum = Month_Max_dict['佛山电信削峰组'] + Month_Max_dict['广州电信削峰组']  # 佛山广州月峰值总和
    # FS_LY_sum = FS_DX_sum + LY_DX_sum  # 佛山广州削峰后总和
    # FS_LY_Difference_value = LY_Difference_value + FS_Difference_value  # 佛山广州削减流量总和
    # FS_LY_Difference_rate = float(
    #     (FS_LY_cut_b - FS_LY_sum * 8 / 1000 / 1000) / FS_LY_cut_b * 100) if FS_LY_cut_b != 0 else 0
    # Mail_Title['FS_LY_Difference_value'] = int(FS_LY_Difference_value * 8 / 1000 / 1000)
    # Mail_Title['FS_LY_Difference_rate'] = FS_LY_Difference_rate
    # #
    # worksheet.write(33, 0, '佛山广州汇总', style1)
    # worksheet.write(33, 3, int(FS_LY_cut_b), style1)
    # worksheet.write(33, 4, int(FS_LY_Month_max_sum), style1)
    # worksheet.write(33, 6, int(float(FS_LY_sum) * 8 / 1000 / 1000), style1)
    # worksheet.write(33, 7, int(float(FS_Out_sum + LY_Out_sum) * 8 / 1000 / 1000), style1)
    # worksheet.write(33, 8,
    #                 int(float(FS_LY_sum) * 8 / 1000 / 1000) - int(float(FS_Out_sum + LY_Out_sum) * 8 / 1000 / 1000),
    #                 style1)
    # worksheet.write(33, 9, int(float(FS_LY_Difference_value) * 8 / 1000 / 1000), style1)
    # worksheet.write(33, 10, '%0.2f' % FS_LY_Difference_rate + '%', style1)

    workbook.save('95削峰流量报告' + Today_Name + r'.xls')


def commands_creation(port_serial, star_time_int, end_time_int):
    #    print(star_time_int,end_time_int)
    timestamp = ' -s%d -e%d ' % (star_time_int, end_time_int)
    return ['rrdtool fetch' + timestamp + r'/var/www/html/rra/' + nnn + r'.rrd MAX' for nnn in port_serial]


def Aggregate_command_reply_list(commands, num_of_cmd):  # 默认保证这几条指令采集的端口正确，时间颗粒度正确
    final_datas = []
    for command in commands:
        final_datas.append(systemdata_transform_std_data(ssh_basic_command_reply_list(command)))
    final_datas.append(final_datas[-1])
    count = 0
    max_in = 0
    max_in_time = ''
    max_out = 0
    max_out_time = ''
    max_inMinusOut = 0
    for bit_time_data in final_datas[0]:  # n1的时间复杂度完成流量速率叠加，最大值取数
        i = 0
        bit_input_sum = 0
        bit_output_sum = 0
        while i < num_of_cmd:
            bit_input_sum += final_datas[i][count][1]
            bit_output_sum += final_datas[i][count][2]
            i += 1
        final_datas[num_of_cmd][count][0] = final_datas[0][count][0]
        final_datas[num_of_cmd][count][1] = bit_input_sum
        final_datas[num_of_cmd][count][2] = bit_output_sum
        if bit_input_sum > max_in:
            max_in = bit_input_sum
            max_in_time = final_datas[0][count][0]
        if bit_output_sum > max_out:
            max_out = bit_output_sum
            max_out_time = final_datas[0][count][0]
        if bit_input_sum - bit_output_sum > max_inMinusOut:
            max_inMinusOut = bit_input_sum - bit_output_sum

        count += 1

    return final_datas, max_in, max_out, max_inMinusOut


# 计算95值
def five_nine_caculate(commb_list):
    count = 0
    for er in commb_list:
        max_num = 0
        if str(er[1]) != 'nan' and str(er[2]) != 'nan':
            max_num = er[1] if (er[1] > er[2]) else er[2]
        elif str(er[1]) == 'nan' and str(er[2]) == 'nan':
            max_num = 0
        elif str(er[1]) == 'nan':
            max_num = er[2]
        else:
            max_num = er[1]
        er.append(max_num)
        count += 1
    after_list = sorted(commb_list, key=lambda x: x[-1], reverse=True)
    #		print(after_list[int(count*0.05)])
    return after_list[int(count * 0.05)][-1]


# 计算out方向95值
def five_nine_caculate_out(commb_list):
    count = 0
    for er in commb_list:
        max_num = 0
        if str(er[2]) != 'nan':
            max_num = er[2]
        else:
            max_num = 0
        er.append(max_num)
        count += 1
    after_list = sorted(commb_list, key=lambda x: x[-1], reverse=True)

    return after_list[int(count * 0.05)][-1]


def systemdata_transform_std_data(orgin_data):
    final_data = []
    for every in orgin_data[2:]:
        bit_data = []
        line = every.split()
        time_local = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(int(line[0][:-1])))
        bit_data.append(time_local)
        bit_data.append(float(line[1]))
        bit_data.append(float(line[2]))
        final_data.append(bit_data)
    return final_data


def ssh_basic_command_reply_list(command):  # 默认采集指令 返回单端口的指定时间段的5分钟颗粒度的取值
    stdin, stdout, stderr = ssh.exec_command(command)

    if len(stderr.readlines()) == 0:
        ans = stdout.readlines()
        return ans
    else:
        print('command in cacti server system error\nThe wrong command is :' + command)


def get_type_file(Today_Name):  # 这里可以更改扩展名如.doc,.py,.zip等等
    # 打印当前的工作目录
    #    print("当前目录为: ", os.getcwd())
    keyword = '%s.xls' % (Today_Name)
    # 列举当前工作目录下的文件名
    files = os.listdir()
    keyword = keyword
    filelist = []
    i = 0
    for file in files:
        if keyword in file:
            i = i + 1
            #            print(i,file)
            filelist.append(file)
    return filelist


def txt_to_html(ini_txt):  # 转换为html格式，对齐文本
    pattern_space = ' '
    pattern_tab = '\n'
    fin_txt = re.sub(pattern_space, r'&ensp;', ini_txt)
    fin_txt = re.sub(pattern_tab, r'<br />', fin_txt)
    print("</h1></body></html><html><body><h1>%s</h1></body></html>" % fin_txt)
    return "</h1></body></html><html><body><h1><font face=\"SimSun\" font size=\"2\" font weight=normal>%s</h1></body></html>" % fin_txt


def send_email(filelist, content=""):  # 发送邮件
    name_xls = '95削峰流量报告%s.xls' % (Today_Name)
    pd.set_option('display.unicode.ambiguous_as_wide', True)
    pd.set_option('display.unicode.east_asian_width', True)
    pd.set_option('display.max_rows', 500)
    pd.set_option('display.max_columns', 500)
    pd.set_option('display.width', 1000)
    df = pd.read_excel(name_xls)
    data = df.head(int(len(ALL_client_list)) + 10)
    # data= df.values
    cont_all_format = pd.DataFrame(data)
    #    print(pd.DataFrame(data))
    #    Bl_label = df.ix[21:29, 6:7]
    #    print(Bl_label)
    #    str_Bl_label = str(Bl_label).split()
    #    print(str_Bl_label[-1])
    smtpHost = 'smtp.163.com'
    sendAddr = '<EMAIL>'
    password = 'EBZARWOKLGPJKBWG'  # EBZARWOKLGPJKBWG
    mail_addr = '<EMAIL>'
#    mail_addr = '<EMAIL>'
    to_addrs = mail_addr.split(',')

    # Cc_mail_addr = '<EMAIL>'
    # Cc_mail_addr = '<EMAIL>'
    # to_Cc_addrs = Cc_mail_addr.split(',')

    Bcc_mail_addr = '<EMAIL>'
    to_Bcc_addrs = Bcc_mail_addr.split(',')

    to_mail_addrs = to_addrs + to_Bcc_addrs

    print(Today_Name)

#    subject = f"95削峰流量报告(cacti129) {Today_Name} 【佛山{Mail_Title['FS_Difference_rate']:.2f}%】  【广州削减{Mail_Title['LY_Difference_rate']:.2f}%】 总削减{Mail_Title['FS_LY_Difference_rate']:.2f}%"
    subject = f"95削峰流量报告(cacti129) {Today_Name} 【佛山{Mail_Title['FS_Difference_rate']:.2f}%({Mail_Title['FS_Difference_value']}M) 】"
#    subject = f"95削峰流量报告(cacti129) {Today_Name} 【佛山{Mail_Title['FS_Difference_rate']:.2f}%({Mail_Title['FS_Difference_value']}) in影响({Mail_Title['FS_in_affect']})】  【广州削减{Mail_Title['LY_Difference_rate']:.2f}%({Mail_Title['LY_Difference_value']}) in影响({Mail_Title['LY_in_affect']})】 总削减{Mail_Title['FS_LY_Difference_rate']:.2f}%({Mail_Title['FS_LY_Difference_value']})"
    # subject = f"广州佛山出端口流量报告(cacti129) {Today_Name} "  # 【佛山{Mail_Title['FS_Difference_rate']:.2f}%({Mail_Title['FS_Difference_value']}) in影响({Mail_Title['FS_in_affect']})】)

    #   content = "获取到所有的值:\n{0}".format(data)
    content = str(cont_all_format)
    content = txt_to_html(content)
    msg = MIMEMultipart()
    msg['from'] = sendAddr
    msg['to'] = ','.join(to_addrs)
    # msg['Cc'] = ','.join(to_Cc_addrs)
    msg['Bcc'] = ','.join(to_Bcc_addrs)
    msg['Subject'] = subject
    txt = MIMEText(content, 'html', 'utf-8')
    msg.attach(txt)  # 添加邮件正文

    for file in filelist:
        #        i = i + 1
        filename = file
        #        print(str(i), filename)
        part = MIMEApplication(open(filename, 'rb').read())
        part.add_header('Content-Disposition', 'attachment', filename=filename)
        msg.attach(part)
    server = smtplib.SMTP(smtpHost, 25)  # SMTP协议默认端口为25
    server.login(sendAddr, password)
    server.sendmail(sendAddr, to_mail_addrs, str(msg))
    print("\n" + "已成功发送" + str(len(filelist)) + "个附件邮件")
    server.quit()


if __name__ == '__main__':
    ssh = paramiko.SSHClient()
    ssh.load_system_host_keys()
    ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    # ssh.connect(hostname='***************', port=22, username='root', password='shHv2^VnT2!.hN', timeout=5,
    #             compress=True)
    ssh.connect(hostname='*************', port=22122, username='root', password='shHv2^VnT2!.hN', timeout=5,
                compress=True)

    Taday_Model = (datetime.datetime.now() - datetime.timedelta(days=1))

#    print(datetime.datetime.now())
    #    Taday_Model = (datetime.datetime.now() - datetime.timedelta(days=5))
    Taday_StyleTime = Taday_Model.strftime("%Y-%m-%d %H:%M:%S")

    Yesterday_Model = (datetime.datetime.now() - datetime.timedelta(days=2))
    Yesterday_StyleTime = Yesterday_Model.strftime("%Y-%m-%d %H:%M:%S")

    Taday_Sta_Format_Time = "%s 00:00:00" % Taday_StyleTime.split()[0]
    Taday_End_Format_Time = "%s 23:59:00" % Taday_StyleTime.split()[0]

    Yesterday_Sta_Format_Time = "%s 00:00:00" % Yesterday_StyleTime.split()[0]
    Yesterday_End_Format_Time = "%s 23:59:00" % Yesterday_StyleTime.split()[0]

    # print(Taday_Sta_Format_Time,Yesterday_Sta_Format_Time)

    Today_Name = Taday_Model.strftime("%Y%m%d")
    Yesterday_Name = Yesterday_Model.strftime("%Y%m%d")

    today_star_int = time.mktime(time.strptime(Taday_Sta_Format_Time, '%Y-%m-%d %H:%M:%S'))
    today_end_int = time.mktime(time.strptime(Taday_End_Format_Time, '%Y-%m-%d %H:%M:%S'))

    Yesterday_star_int = time.mktime(time.strptime(Yesterday_Sta_Format_Time, '%Y-%m-%d %H:%M:%S'))
    Yesterday_end_int = time.mktime(time.strptime(Yesterday_End_Format_Time, '%Y-%m-%d %H:%M:%S'))

    Day_Now = time.localtime()
    if int(datetime.datetime.now().month) > int(Taday_Model.month):
        Month_StyleTime = '%d-%02d-01' % (Day_Now.tm_year, Day_Now.tm_mon - 1)
    else:
        Month_StyleTime = '%d-%02d-01' % (Day_Now.tm_year, Day_Now.tm_mon)
    Month_Star_time = "%s 00:00:00" % Month_StyleTime.split()[0]

    Month_star_int = time.mktime(time.strptime(Month_Star_time, '%Y-%m-%d %H:%M:%S'))

#    print(Today_Name, Yesterday_Name, today_star_int, today_end_int, Yesterday_star_int, Yesterday_end_int,Month_star_int)

    Main_excel_creat(Today_Name, Yesterday_Name, today_star_int, today_end_int, Yesterday_star_int, Yesterday_end_int,Month_star_int)


    ssh.close()
    filelist_xls = get_type_file(Today_Name)  # 获取xls所有文件
    send_email(filelist_xls)  # 发送xls所有文件
