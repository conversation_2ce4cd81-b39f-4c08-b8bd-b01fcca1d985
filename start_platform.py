#!/usr/bin/env python
"""
脚本执行与管理平台启动脚本
用于快速启动和配置平台
"""

import os
import sys
import subprocess
import time


def run_command(command, description=""):
    """执行命令并显示结果"""
    if description:
        print(f"\n{'='*50}")
        print(f"执行: {description}")
        print(f"命令: {command}")
        print(f"{'='*50}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, 
                              capture_output=True, text=True)
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"错误: {e}")
        if e.stderr:
            print(f"错误信息: {e.stderr}")
        return False


def check_requirements():
    """检查系统要求"""
    print("检查系统要求...")
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("错误: 需要Python 3.8或更高版本")
        return False
    
    print(f"✓ Python版本: {sys.version}")
    
    # 检查必要的包
    required_packages = ['django', 'celery', 'redis']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package} 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"✗ {package} 未安装")
    
    if missing_packages:
        print(f"\n请先安装缺失的包:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True


def setup_database():
    """设置数据库"""
    print("\n设置数据库...")
    
    # 生成迁移文件
    if not run_command("python manage.py makemigrations", "生成数据库迁移文件"):
        return False
    
    # 执行迁移
    if not run_command("python manage.py migrate", "执行数据库迁移"):
        return False
    
    return True


def setup_initial_data():
    """设置初始数据"""
    print("\n设置初始数据...")
    
    # 创建超级用户和示例数据
    if not run_command("python manage.py setup_platform --create-superuser", 
                      "创建超级用户和示例数据"):
        return False
    
    return True


def start_services():
    """启动服务"""
    print("\n启动服务...")
    
    # 检查Redis是否运行
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0)
        r.ping()
        print("✓ Redis服务正在运行")
    except Exception as e:
        print("✗ Redis服务未运行，请先启动Redis")
        print("Windows: 下载并启动Redis服务器")
        print("Linux/Mac: sudo systemctl start redis 或 redis-server")
        return False
    
    print("\n平台已准备就绪！")
    print("\n启动说明:")
    print("1. 启动Django开发服务器:")
    print("   python manage.py runserver")
    print("\n2. 在新终端中启动Celery Worker:")
    print("   celery -A script_platform worker --loglevel=info")
    print("\n3. 在新终端中启动Celery Beat (定时任务调度器):")
    print("   celery -A script_platform beat --loglevel=info")
    print("\n4. 访问管理界面:")
    print("   http://127.0.0.1:8000/admin/")
    print("   用户名: admin")
    print("   密码: admin123")
    
    return True


def main():
    """主函数"""
    print("脚本执行与管理平台 - 启动脚本")
    print("="*50)
    
    # 检查系统要求
    if not check_requirements():
        sys.exit(1)
    
    # 设置数据库
    if not setup_database():
        print("数据库设置失败")
        sys.exit(1)
    
    # 设置初始数据
    if not setup_initial_data():
        print("初始数据设置失败")
        sys.exit(1)
    
    # 启动服务
    if not start_services():
        print("服务启动失败")
        sys.exit(1)
    
    print("\n✓ 平台启动脚本执行完成！")


if __name__ == "__main__":
    main()
