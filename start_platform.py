#!/usr/bin/env python
"""
脚本执行与管理平台启动脚本
用于快速启动和配置平台
"""

import os
import sys
import subprocess
import time


def run_command(command, description=""):
    """执行命令并显示结果"""
    if description:
        print(f"\n{'='*50}")
        print(f"执行: {description}")
        print(f"命令: {command}")
        print(f"{'='*50}")

    try:
        result = subprocess.run(command, shell=True, check=True,
                              capture_output=True, text=True)
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"错误: {e}")
        if e.stderr:
            print(f"错误信息: {e.stderr}")
        return False


def check_requirements():
    """检查系统要求"""
    print("检查系统要求...")

    # 检查Python版本
    if sys.version_info < (3, 6):
        print("错误: 需要Python 3.6或更高版本")
        return False

    print(f"✓ Python版本: {sys.version}")

    # 检查Django
    try:
        import django
        print(f"✓ Django 已安装 (版本: {django.get_version()})")
    except ImportError:
        print("✗ Django 未安装")
        print("请运行: pip install django==3.2.3")
        return False

    return True


def setup_database():
    """设置数据库"""
    print("\n设置数据库...")

    # 生成迁移文件
    if not run_command("python manage.py makemigrations", "生成数据库迁移文件"):
        return False

    # 执行迁移
    if not run_command("python manage.py migrate", "执行数据库迁移"):
        return False

    return True


def setup_initial_data():
    """设置初始数据"""
    print("\n设置初始数据...")

    # 创建超级用户和示例数据
    if not run_command("python manage.py setup_platform --create-superuser",
                      "创建超级用户和示例数据"):
        return False

    return True


def start_services():
    """启动服务"""
    print("\n平台已准备就绪！")
    print("\n启动说明:")
    print("1. 启动Django开发服务器:")
    print("   python manage.py runserver")
    print("\n2. 访问管理界面:")
    print("   http://127.0.0.1:8000/admin/")
    print("   用户名: admin")
    print("   密码: admin")
    print("\n注意:")
    print("- 当前使用SQLite数据库（适合开发测试）")
    print("- 脚本执行为同步模式（适合测试）")
    print("- 生产环境请安装MySQL、Redis和Celery")

    return True


def main():
    """主函数"""
    print("脚本执行与管理平台 - 启动脚本")
    print("="*50)

    # 检查系统要求
    if not check_requirements():
        sys.exit(1)

    # 设置数据库
    if not setup_database():
        print("数据库设置失败")
        sys.exit(1)

    # 设置初始数据
    if not setup_initial_data():
        print("初始数据设置失败")
        sys.exit(1)

    # 启动服务
    if not start_services():
        print("服务启动失败")
        sys.exit(1)

    print("\n✓ 平台启动脚本执行完成！")


if __name__ == "__main__":
    main()
