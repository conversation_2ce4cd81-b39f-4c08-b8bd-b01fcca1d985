# 脚本执行与管理平台

一个基于Django的Python脚本执行和管理平台，支持脚本上传、参数配置、定时执行、结果查看等功能。

## 功能特性

### 核心功能
- **脚本管理**: 上传、编辑、删除Python脚本
- **参数配置**: 为脚本定义输入参数（文本、数字、文件、选择等）
- **手动执行**: 通过Web界面手动执行脚本
- **定时任务**: 使用Cron表达式配置定时执行
- **结果查看**: 查看执行结果、输出文件、错误信息
- **执行历史**: 完整的执行记录和统计信息

### 技术特性
- **异步执行**: 使用Celery实现脚本异步执行
- **并发控制**: 支持最多10个脚本并发执行
- **文件管理**: 支持输入文件上传和输出文件下载
- **用户权限**: 基于Django的用户权限管理
- **美观界面**: 使用SimpleUI美化的管理界面

## 系统要求

- Python 3.8+
- MySQL 5.7+
- Redis 6.0+
- Django 3.2+

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置数据库

编辑 `script_platform/settings.py` 中的数据库配置：

```python
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'script_platform',
        'USER': 'your_username',
        'PASSWORD': 'your_password',
        'HOST': 'localhost',
        'PORT': '3306',
    }
}
```

### 3. 启动平台

运行启动脚本：

```bash
python start_platform.py
```

或手动执行以下步骤：

```bash
# 数据库迁移
python manage.py makemigrations
python manage.py migrate

# 创建超级用户和示例数据
python manage.py setup_platform --create-superuser

# 启动Django服务器
python manage.py runserver

# 在新终端启动Celery Worker
celery -A script_platform worker --loglevel=info

# 在新终端启动Celery Beat
celery -A script_platform beat --loglevel=info
```

### 4. 访问平台

打开浏览器访问: http://127.0.0.1:8000/admin/

默认登录信息：
- 用户名: admin
- 密码: admin123

## 使用指南

### 脚本管理

1. **上传脚本**
   - 在"脚本管理"中点击"增加脚本"
   - 填写脚本名称、描述，上传Python文件
   - 系统会自动设置当前用户为脚本负责人

2. **配置参数**
   - 在脚本详情页面添加参数定义
   - 支持的参数类型：文本、数字、布尔值、文件、选择
   - 可设置参数是否必填、默认值等

3. **执行脚本**
   - 在脚本列表中点击"立即执行"
   - 填写所需参数
   - 系统会异步执行脚本并记录结果

### 定时任务

1. **创建定时任务**
   - 在"任务调度"中点击"增加定时任务"
   - 选择要执行的脚本
   - 设置Cron表达式（如：`0 9 * * *` 表示每天9点执行）
   - 配置执行参数

2. **Cron表达式示例**
   - `0 */2 * * *`: 每2小时执行一次
   - `30 8 * * 1-5`: 工作日上午8:30执行
   - `0 0 1 * *`: 每月1号午夜执行

### 查看结果

1. **执行记录**
   - 在"执行记录"中查看所有执行历史
   - 可按状态、脚本、时间等筛选
   - 点击"查看结果"查看详细信息

2. **结果内容**
   - 标准输出和错误输出
   - 执行时长和返回码
   - 生成的输出文件（可下载）
   - 执行统计信息

## 脚本开发规范

### 参数处理

脚本应使用argparse处理命令行参数：

```python
import argparse

def main():
    parser = argparse.ArgumentParser(description='脚本描述')
    parser.add_argument('--param1', type=str, help='参数1说明')
    parser.add_argument('--param2', type=int, default=10, help='参数2说明')
    
    args = parser.parse_args()
    
    # 脚本逻辑
    print(f"参数1: {args.param1}")
    print(f"参数2: {args.param2}")

if __name__ == '__main__':
    main()
```

### 输出文件

脚本可以生成输出文件，建议使用环境变量获取输出目录：

```python
import os

# 获取输出目录
output_dir = os.environ.get('SCRIPT_OUTPUT_DIR', './output')
os.makedirs(output_dir, exist_ok=True)

# 生成输出文件
output_file = os.path.join(output_dir, 'result.csv')
# ... 写入文件
```

### 错误处理

脚本应正确处理错误并返回适当的退出码：

```python
import sys

try:
    # 脚本逻辑
    pass
except Exception as e:
    print(f"错误: {str(e)}", file=sys.stderr)
    sys.exit(1)  # 非零退出码表示失败

sys.exit(0)  # 零退出码表示成功
```

## 配置说明

### 主要配置项

在 `script_platform/settings.py` 中可以配置：

- `SCRIPT_EXECUTION_TIMEOUT`: 脚本执行超时时间（秒）
- `MAX_CONCURRENT_SCRIPTS`: 最大并发执行脚本数
- `SCRIPT_UPLOAD_PATH`: 脚本上传路径
- `SCRIPT_OUTPUT_PATH`: 脚本输出文件路径

### Celery配置

- `CELERY_BROKER_URL`: Redis连接URL
- `CELERY_RESULT_BACKEND`: 结果存储后端
- `CELERY_TIMEZONE`: 时区设置

## 故障排除

### 常见问题

1. **脚本执行失败**
   - 检查脚本文件是否存在
   - 确认Python环境和依赖包
   - 查看错误输出信息

2. **定时任务不执行**
   - 确认Celery Beat服务正在运行
   - 检查Cron表达式是否正确
   - 查看Celery日志

3. **文件上传失败**
   - 检查媒体文件目录权限
   - 确认文件大小限制
   - 查看Django日志

### 日志查看

- Django日志: 在终端查看runserver输出
- Celery日志: 在Celery Worker终端查看
- 系统日志: 在管理界面的"系统日志"中查看

## 开发和扩展

### 添加新功能

1. 修改模型: 在 `script_manager/models.py` 中添加新模型
2. 更新Admin: 在 `script_manager/admin.py` 中配置管理界面
3. 添加视图: 在 `script_manager/views.py` 中添加自定义视图
4. 配置URL: 在 `script_manager/urls.py` 中添加路由

### 自定义脚本执行器

可以扩展 `script_manager/tasks.py` 中的执行逻辑，支持更多脚本类型或执行环境。

## 许可证

MIT License

## 支持

如有问题或建议，请提交Issue或联系开发团队。
