import numpy as np

P = 480000
annual_rate = 0.03
r = annual_rate / 12
n = 360
A = P * r * (1 + r)**n / ((1 + r)**n - 1)

fixed_principal = P / n
balance_emi = P
balance_ep = P

cum_interest_emi_5 = cum_interest_ep_5 = 0
cum_interest_emi_10 = cum_interest_ep_10 = 0

for month in range(1, n + 1):
    # 等额本息
    interest_emi = balance_emi * r
    principal_emi = A - interest_emi
    balance_emi -= principal_emi

    # 等额本金
    interest_ep = balance_ep * r
    balance_ep -= fixed_principal

    # 累计前5年/10年利息
    if month <= 60:
        cum_interest_emi_5 += interest_emi
        cum_interest_ep_5 += interest_ep
    if month <= 120:
        cum_interest_emi_10 += interest_emi
        cum_interest_ep_10 += interest_ep

print("前5年 等额本金利息:", cum_interest_ep_5)
print("前5年 等额本息利息:", cum_interest_emi_5)
print("前10年 等额本金利息:", cum_interest_ep_10)
print("前10年 等额本息利息:", cum_interest_emi_10)
