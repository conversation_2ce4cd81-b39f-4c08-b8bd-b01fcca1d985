#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
演示脚本：生成HTML结果和多种输出文件
这个脚本演示了如何使用新的文件管理系统
"""

import argparse
import os
import sys
import pandas as pd
import json
from datetime import datetime


def generate_html_result(data, output_dir):
    """生成HTML结果文件"""
    html_content = f"""
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>脚本执行结果</title>
        <style>
            body {{
                font-family: 'Microsoft YaHei', Arial, sans-serif;
                margin: 20px;
                background-color: #f5f5f5;
            }}
            .container {{
                background: white;
                padding: 20px;
                border-radius: 8px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }}
            .header {{
                color: #2c3e50;
                border-bottom: 2px solid #3498db;
                padding-bottom: 10px;
                margin-bottom: 20px;
            }}
            .summary {{
                background: #ecf0f1;
                padding: 15px;
                border-radius: 5px;
                margin: 15px 0;
            }}
            .data-table {{
                width: 100%;
                border-collapse: collapse;
                margin: 20px 0;
            }}
            .data-table th, .data-table td {{
                border: 1px solid #bdc3c7;
                padding: 12px;
                text-align: left;
            }}
            .data-table th {{
                background-color: #3498db;
                color: white;
                font-weight: bold;
            }}
            .data-table tr:nth-child(even) {{
                background-color: #f8f9fa;
            }}
            .success {{
                color: #27ae60;
                font-weight: bold;
            }}
            .info {{
                color: #3498db;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <h1 class="header">📊 脚本执行结果报告</h1>
            
            <div class="summary">
                <h2>📋 执行摘要</h2>
                <p><strong>执行时间：</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                <p><strong>数据行数：</strong> <span class="info">{len(data)}</span></p>
                <p><strong>执行状态：</strong> <span class="success">✅ 成功完成</span></p>
            </div>
            
            <h2>📈 数据详情</h2>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>序号</th>
                        <th>项目名称</th>
                        <th>数值</th>
                        <th>类别</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody>
    """
    
    for i, row in enumerate(data, 1):
        status = "✅ 正常" if row['value'] > 50 else "⚠️ 注意"
        html_content += f"""
                    <tr>
                        <td>{i}</td>
                        <td>{row['name']}</td>
                        <td>{row['value']}</td>
                        <td>{row['category']}</td>
                        <td>{status}</td>
                    </tr>
        """
    
    html_content += """
                </tbody>
            </table>
            
            <div class="summary">
                <h2>📊 统计信息</h2>
                <p><strong>平均值：</strong> {:.2f}</p>
                <p><strong>最大值：</strong> {}</p>
                <p><strong>最小值：</strong> {}</p>
            </div>
            
            <p style="text-align: center; color: #7f8c8d; margin-top: 30px;">
                <small>由脚本执行与管理平台生成 - {}</small>
            </p>
        </div>
    </body>
    </html>
    """.format(
        sum(row['value'] for row in data) / len(data),
        max(row['value'] for row in data),
        min(row['value'] for row in data),
        datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    )
    
    # 保存HTML结果文件
    html_file = os.path.join(output_dir, '结果正文.html')
    with open(html_file, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"✅ HTML结果文件已生成: {html_file}")
    return html_file


def main():
    parser = argparse.ArgumentParser(description='演示HTML输出和文件管理的脚本')
    parser.add_argument('--data_count', type=int, default=10, help='生成的数据行数')
    parser.add_argument('--output_format', type=str, default='all', 
                       choices=['csv', 'excel', 'json', 'all'], help='输出格式')
    parser.add_argument('--category_filter', type=str, help='类别过滤器')
    
    args = parser.parse_args()
    
    print(f"🚀 开始执行演示脚本: {datetime.now()}")
    print(f"📝 参数: {vars(args)}")
    
    # 获取输出目录（由平台设置的环境变量）
    output_dir = os.environ.get('SCRIPT_OUTPUT_DIR', './output')
    execution_id = os.environ.get('EXECUTION_ID', 'demo')
    
    print(f"📁 输出目录: {output_dir}")
    print(f"🆔 执行ID: {execution_id}")
    
    try:
        # 生成示例数据
        import random
        categories = ['A类', 'B类', 'C类', 'D类']
        
        data = []
        for i in range(args.data_count):
            data.append({
                'name': f'项目_{i+1:03d}',
                'value': random.randint(10, 100),
                'category': random.choice(categories),
                'date': datetime.now().strftime('%Y-%m-%d')
            })
        
        # 应用类别过滤器
        if args.category_filter:
            original_count = len(data)
            data = [row for row in data if args.category_filter in row['category']]
            print(f"🔍 应用过滤器 '{args.category_filter}': {len(data)} 行 (原始: {original_count} 行)")
        
        # 生成HTML结果文件（必需）
        html_file = generate_html_result(data, output_dir)
        
        # 生成其他格式的输出文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        if args.output_format in ['csv', 'all']:
            df = pd.DataFrame(data)
            csv_file = os.path.join(output_dir, f'数据报告_{timestamp}.csv')
            df.to_csv(csv_file, index=False, encoding='utf-8-sig')
            print(f"📄 CSV文件已生成: {csv_file}")
        
        if args.output_format in ['excel', 'all']:
            df = pd.DataFrame(data)
            excel_file = os.path.join(output_dir, f'数据报告_{timestamp}.xlsx')
            df.to_excel(excel_file, index=False)
            print(f"📊 Excel文件已生成: {excel_file}")
        
        if args.output_format in ['json', 'all']:
            json_file = os.path.join(output_dir, f'数据报告_{timestamp}.json')
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"🔧 JSON文件已生成: {json_file}")
        
        # 生成统计摘要文件
        stats = {
            'total_records': len(data),
            'average_value': sum(row['value'] for row in data) / len(data),
            'max_value': max(row['value'] for row in data),
            'min_value': min(row['value'] for row in data),
            'categories': list(set(row['category'] for row in data)),
            'execution_time': datetime.now().isoformat(),
            'execution_id': execution_id
        }
        
        stats_file = os.path.join(output_dir, f'统计摘要_{timestamp}.json')
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)
        print(f"📈 统计摘要已生成: {stats_file}")
        
        print(f"✅ 脚本执行完成: {datetime.now()}")
        print(f"📊 处理了 {len(data)} 条记录")
        print(f"📁 输出文件保存在: {output_dir}")
        
        return 0
        
    except Exception as e:
        print(f"❌ 错误: {str(e)}", file=sys.stderr)
        return 1


if __name__ == '__main__':
    sys.exit(main())
