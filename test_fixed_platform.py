#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试修复后的平台功能
"""

import argparse
import os
import sys
import pandas as pd
import json
from datetime import datetime


def main():
    parser = argparse.ArgumentParser(description='测试修复后的平台功能')
    parser.add_argument('--test_type', type=str, default='all', 
                       choices=['html', 'files', 'download', 'all'], help='测试类型')
    
    args = parser.parse_args()
    
    print(f"🚀 开始测试修复后的平台功能: {datetime.now()}")
    
    # 获取输出目录
    output_dir = os.environ.get('SCRIPT_OUTPUT_DIR', './output')
    execution_id = os.environ.get('EXECUTION_ID', 'test')
    
    print(f"📁 输出目录: {output_dir}")
    print(f"🆔 执行ID: {execution_id}")
    
    try:
        # 测试1: HTML结果文件生成
        if args.test_type in ['html', 'all']:
            print("\n📝 测试1: 生成HTML结果文件")
            html_content = f"""
            <!DOCTYPE html>
            <html lang="zh-CN">
            <head>
                <meta charset="UTF-8">
                <title>平台修复测试报告</title>
                <style>
                    body {{
                        font-family: 'Microsoft YaHei', Arial, sans-serif;
                        margin: 20px;
                        background-color: #f8f9fa;
                    }}
                    .container {{
                        background: white;
                        padding: 30px;
                        border-radius: 8px;
                        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                    }}
                    .header {{
                        color: #28a745;
                        border-bottom: 3px solid #28a745;
                        padding-bottom: 15px;
                        margin-bottom: 25px;
                    }}
                    .test-result {{
                        background: #d4edda;
                        border: 1px solid #c3e6cb;
                        border-radius: 5px;
                        padding: 15px;
                        margin: 15px 0;
                    }}
                    .test-item {{
                        margin: 10px 0;
                        padding: 10px;
                        border-left: 4px solid #28a745;
                        background: #f8f9fa;
                    }}
                    .success {{
                        color: #28a745;
                        font-weight: bold;
                    }}
                </style>
            </head>
            <body>
                <div class="container">
                    <h1 class="header">✅ 平台修复测试报告</h1>
                    
                    <div class="test-result">
                        <h2>🔧 修复项目测试结果</h2>
                        
                        <div class="test-item">
                            <h3>1. 执行状态样式统一</h3>
                            <p class="success">✅ 成功状态样式已统一</p>
                            <p>现在所有成功状态都使用一致的绿色背景和边框样式</p>
                        </div>
                        
                        <div class="test-item">
                            <h3>2. 文件下载链接可见性</h3>
                            <p class="success">✅ 文件下载链接现在清晰可见</p>
                            <p>使用绿色背景，白色文字，确保文件名完全可读</p>
                        </div>
                        
                        <div class="test-item">
                            <h3>3. 文件下载扩展名保持</h3>
                            <p class="success">✅ 下载文件保持原始扩展名</p>
                            <p>使用FileResponse和正确的MIME类型处理</p>
                        </div>
                        
                        <div class="test-item">
                            <h3>4. 文件重复存储问题</h3>
                            <p class="success">✅ 已消除文件重复存储</p>
                            <p>脚本直接在输出目录工作，避免文件复制</p>
                        </div>
                    </div>
                    
                    <div class="test-result">
                        <h2>📊 测试执行信息</h2>
                        <p><strong>执行时间:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                        <p><strong>执行ID:</strong> {execution_id}</p>
                        <p><strong>输出目录:</strong> {output_dir}</p>
                        <p><strong>测试状态:</strong> <span class="success">全部通过</span></p>
                    </div>
                </div>
            </body>
            </html>
            """
            
            html_file = os.path.join(output_dir, '结果正文.html')
            with open(html_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
            print(f"✅ HTML结果文件已生成: {html_file}")
        
        # 测试2: 生成多种格式的测试文件
        if args.test_type in ['files', 'all']:
            print("\n📄 测试2: 生成多种格式的测试文件")
            
            # 生成测试数据
            test_data = [
                {'项目': '状态样式修复', '状态': '完成', '优先级': '高', '测试结果': '通过'},
                {'项目': '下载链接修复', '状态': '完成', '优先级': '高', '测试结果': '通过'},
                {'项目': '扩展名保持', '状态': '完成', '优先级': '中', '测试结果': '通过'},
                {'项目': '重复文件清理', '状态': '完成', '优先级': '高', '测试结果': '通过'},
            ]
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # Excel文件
            df = pd.DataFrame(test_data)
            excel_file = os.path.join(output_dir, f'平台修复测试报告_{timestamp}.xlsx')
            df.to_excel(excel_file, index=False)
            print(f"📊 Excel文件已生成: {excel_file}")
            
            # CSV文件
            csv_file = os.path.join(output_dir, f'平台修复测试报告_{timestamp}.csv')
            df.to_csv(csv_file, index=False, encoding='utf-8-sig')
            print(f"📄 CSV文件已生成: {csv_file}")
            
            # JSON文件
            json_file = os.path.join(output_dir, f'平台修复测试报告_{timestamp}.json')
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(test_data, f, ensure_ascii=False, indent=2)
            print(f"🔧 JSON文件已生成: {json_file}")
            
            # 文本文件
            txt_file = os.path.join(output_dir, f'平台修复测试报告_{timestamp}.txt')
            with open(txt_file, 'w', encoding='utf-8') as f:
                f.write("平台修复测试报告\\n")
                f.write("=" * 50 + "\\n")
                f.write(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\\n")
                f.write(f"执行ID: {execution_id}\\n\\n")
                
                for i, item in enumerate(test_data, 1):
                    f.write(f"{i}. {item['项目']}\\n")
                    f.write(f"   状态: {item['状态']}\\n")
                    f.write(f"   优先级: {item['优先级']}\\n")
                    f.write(f"   测试结果: {item['测试结果']}\\n\\n")
                
                f.write("所有修复项目测试通过！\\n")
            print(f"📝 文本文件已生成: {txt_file}")
        
        print(f"\\n✅ 测试完成: {datetime.now()}")
        print(f"📁 所有文件已保存到: {output_dir}")
        
        return 0
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}", file=sys.stderr)
        return 1


if __name__ == '__main__':
    sys.exit(main())
