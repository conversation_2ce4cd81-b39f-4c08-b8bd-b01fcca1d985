# Generated by Django 3.2.3 on 2025-05-29 08:02

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Script',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='脚本名称')),
                ('description', models.TextField(blank=True, verbose_name='脚本描述')),
                ('file', models.FileField(upload_to='scripts/', verbose_name='脚本文件')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
                ('owner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='负责人')),
            ],
            options={
                'verbose_name': '脚本',
                'verbose_name_plural': '脚本管理',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SystemLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('level', models.CharField(choices=[('DEBUG', 'Debug'), ('INFO', 'Info'), ('WARNING', 'Warning'), ('ERROR', 'Error'), ('CRITICAL', 'Critical')], max_length=20, verbose_name='日志级别')),
                ('message', models.TextField(verbose_name='日志消息')),
                ('module', models.CharField(max_length=100, verbose_name='模块')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('extra_data', models.TextField(blank=True, default='{}', verbose_name='额外数据')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '系统日志',
                'verbose_name_plural': '系统日志',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ScriptParameter',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='参数名')),
                ('display_name', models.CharField(max_length=100, verbose_name='显示名称')),
                ('parameter_type', models.CharField(choices=[('text', '文本'), ('number', '数字'), ('boolean', '布尔值'), ('file', '文件'), ('select', '选择')], max_length=20, verbose_name='参数类型')),
                ('is_required', models.BooleanField(default=True, verbose_name='是否必填')),
                ('default_value', models.TextField(blank=True, verbose_name='默认值')),
                ('choices', models.TextField(blank=True, help_text='选择类型的选项，每行一个', verbose_name='选项')),
                ('help_text', models.TextField(blank=True, verbose_name='帮助文本')),
                ('order', models.IntegerField(default=0, verbose_name='排序')),
                ('script', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='parameters', to='script_manager.script', verbose_name='所属脚本')),
            ],
            options={
                'verbose_name': '脚本参数',
                'verbose_name_plural': '脚本参数',
                'ordering': ['order', 'id'],
            },
        ),
        migrations.CreateModel(
            name='ScheduledTask',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='任务名称')),
                ('cron_expression', models.CharField(max_length=100, verbose_name='Cron表达式')),
                ('parameters', models.TextField(blank=True, default='{}', verbose_name='执行参数')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('last_run_at', models.DateTimeField(blank=True, null=True, verbose_name='最后执行时间')),
                ('next_run_at', models.DateTimeField(blank=True, null=True, verbose_name='下次执行时间')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='创建者')),
                ('script', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='script_manager.script', verbose_name='执行脚本')),
            ],
            options={
                'verbose_name': '定时任务',
                'verbose_name_plural': '任务调度',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ExecutionRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('execution_id', models.CharField(max_length=100, unique=True, verbose_name='执行ID')),
                ('status', models.CharField(choices=[('pending', '等待中'), ('running', '运行中'), ('success', '成功'), ('failed', '失败'), ('cancelled', '已取消')], default='pending', max_length=20, verbose_name='执行状态')),
                ('trigger_type', models.CharField(choices=[('manual', '手动执行'), ('scheduled', '定时执行')], max_length=20, verbose_name='触发方式')),
                ('parameters', models.TextField(blank=True, default='{}', verbose_name='执行参数')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('started_at', models.DateTimeField(blank=True, null=True, verbose_name='开始时间')),
                ('finished_at', models.DateTimeField(blank=True, null=True, verbose_name='结束时间')),
                ('stdout', models.TextField(blank=True, verbose_name='标准输出')),
                ('stderr', models.TextField(blank=True, verbose_name='错误输出')),
                ('return_code', models.IntegerField(blank=True, null=True, verbose_name='返回码')),
                ('output_files', models.TextField(blank=True, default='[]', verbose_name='输出文件')),
                ('scheduled_task', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='script_manager.scheduledtask', verbose_name='关联定时任务')),
                ('script', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='script_manager.script', verbose_name='执行脚本')),
                ('triggered_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='触发者')),
            ],
            options={
                'verbose_name': '执行记录',
                'verbose_name_plural': '执行记录',
                'ordering': ['-created_at'],
            },
        ),
    ]
