from django.urls import path
from . import views

app_name = 'script_manager'

urlpatterns = [
    # 脚本执行相关
    path('execute/<int:script_id>/', views.execute_script_view, name='execute_script'),
    path('result/<int:record_id>/', views.view_execution_result, name='view_execution_result'),
    path('download/<int:record_id>/<str:file_name>/', views.download_output_file, name='download_output_file'),
    path('cancel/<int:record_id>/', views.cancel_execution, name='cancel_execution'),
    
    # 仪表板
    path('dashboard/', views.DashboardView.as_view(), name='dashboard'),
]
