# 脚本执行与管理平台 - 部署指南

## 快速开始（开发环境）

### 1. 环境要求
- Python 3.6+
- Django 3.2+

### 2. 快速启动
```bash
# 1. 运行启动脚本
python start_platform.py

# 2. 启动开发服务器
python manage.py runserver

# 3. 访问管理界面
# 浏览器打开: http://127.0.0.1:8000/admin/
# 用户名: admin
# 密码: admin
```

## 生产环境部署

### 1. 系统要求
- Python 3.8+
- MySQL 5.7+
- Redis 6.0+
- Nginx (可选)

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 配置数据库
编辑 `script_platform/settings.py`:
```python
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'script_platform',
        'USER': 'your_username',
        'PASSWORD': 'your_password',
        'HOST': 'localhost',
        'PORT': '3306',
    }
}
```

### 4. 启用Celery
在 `script_platform/settings.py` 中取消注释:
```python
INSTALLED_APPS = [
    'simpleui',  # 取消注释
    # ... 其他应用
    'django_celery_beat',  # 取消注释
    'django_celery_results',  # 取消注释
    # ...
]

# 取消注释Celery配置
CELERY_BROKER_URL = 'redis://localhost:6379/0'
CELERY_RESULT_BACKEND = 'django-db'
# ...
```

### 5. 数据库迁移
```bash
python manage.py makemigrations
python manage.py migrate
python manage.py createsuperuser
```

### 6. 启动服务

#### 启动Django应用
```bash
# 开发环境
python manage.py runserver

# 生产环境（使用gunicorn）
pip install gunicorn
gunicorn script_platform.wsgi:application --bind 0.0.0.0:8000
```

#### 启动Celery Worker
```bash
celery -A script_platform worker --loglevel=info
```

#### 启动Celery Beat（定时任务）
```bash
celery -A script_platform beat --loglevel=info
```

### 7. Nginx配置（可选）
```nginx
server {
    listen 80;
    server_name your_domain.com;

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /static/ {
        alias /path/to/your/project/staticfiles/;
    }

    location /media/ {
        alias /path/to/your/project/media/;
    }
}
```

## 功能测试

### 1. 上传示例脚本
1. 登录管理界面
2. 进入"脚本管理"
3. 点击"增加脚本"
4. 上传 `media/scripts/example_script.py`
5. 配置脚本参数

### 2. 手动执行脚本
1. 在脚本列表中点击"立即执行"
2. 填写参数
3. 查看执行结果

### 3. 配置定时任务
1. 进入"任务调度"
2. 创建新的定时任务
3. 设置Cron表达式
4. 配置执行参数

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库配置
   - 确认数据库服务正在运行
   - 验证用户权限

2. **Celery任务不执行**
   - 确认Redis服务正在运行
   - 检查Celery Worker是否启动
   - 查看Celery日志

3. **脚本执行失败**
   - 检查脚本文件权限
   - 确认Python环境
   - 查看执行记录中的错误信息

4. **文件上传失败**
   - 检查media目录权限
   - 确认文件大小限制
   - 查看Django日志

### 日志查看
- Django日志: 在runserver终端查看
- Celery日志: 在Celery Worker终端查看
- 系统日志: 管理界面 -> 系统日志

## 性能优化

### 1. 数据库优化
- 定期清理旧的执行记录
- 为常用查询添加索引
- 使用数据库连接池

### 2. 文件存储优化
- 定期清理旧的输出文件
- 使用对象存储（如AWS S3）
- 压缩大文件

### 3. 并发优化
- 调整Celery Worker数量
- 配置合适的并发限制
- 使用负载均衡

## 安全考虑

### 1. 脚本安全
- 验证上传的脚本文件
- 限制脚本执行权限
- 使用沙箱环境

### 2. 访问控制
- 配置强密码策略
- 启用HTTPS
- 限制管理界面访问IP

### 3. 数据安全
- 定期备份数据库
- 加密敏感信息
- 审计用户操作

## 监控和维护

### 1. 系统监控
- 监控服务器资源使用
- 监控数据库性能
- 监控脚本执行状态

### 2. 定期维护
- 清理旧数据
- 更新依赖包
- 备份重要数据

### 3. 扩展功能
- 添加邮件通知
- 集成监控系统
- 开发API接口
