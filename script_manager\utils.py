import os
import json
from django.contrib.auth.models import User
from .models import SystemLog


def log_system_event(level, message, module, user_id=None, extra_data=None):
    """
    记录系统事件日志
    """
    try:
        user = None
        if user_id:
            try:
                user = User.objects.get(id=user_id)
            except User.DoesNotExist:
                pass

        log_entry = SystemLog.objects.create(
            level=level,
            message=message,
            module=module,
            user=user,
        )
        log_entry.set_extra_data(extra_data or {})
        log_entry.save()
    except Exception as e:
        # 如果日志记录失败，至少打印到控制台
        print(f"Failed to log system event: {e}")


def validate_cron_expression(cron_expr):
    """
    验证Cron表达式的有效性
    """
    try:
        from crontab import CronTab
        # 简单验证：检查是否有5个字段
        parts = cron_expr.strip().split()
        if len(parts) != 5:
            return False, "Cron表达式必须包含5个字段"

        # 可以添加更详细的验证逻辑
        return True, "有效的Cron表达式"
    except Exception as e:
        return False, f"Cron表达式验证失败: {str(e)}"


def get_script_parameters_form_data(script):
    """
    获取脚本参数的表单数据结构
    """
    form_data = []
    for param in script.parameters.all().order_by('order'):
        field_data = {
            'name': param.name,
            'display_name': param.display_name,
            'type': param.parameter_type,
            'required': param.is_required,
            'default_value': param.default_value,
            'help_text': param.help_text
        }

        if param.parameter_type == 'select' and param.choices:
            field_data['choices'] = [
                choice.strip() for choice in param.choices.split('\n')
                if choice.strip()
            ]

        form_data.append(field_data)

    return form_data


def prepare_script_execution_parameters(script, form_data):
    """
    准备脚本执行参数
    """
    parameters = {}

    for param in script.parameters.all():
        value = form_data.get(param.name)

        if value is not None:
            # 根据参数类型进行转换
            if param.parameter_type == 'number':
                try:
                    value = float(value) if '.' in str(value) else int(value)
                except (ValueError, TypeError):
                    value = param.default_value
            elif param.parameter_type == 'boolean':
                value = str(value).lower() in ['true', '1', 'yes', 'on']

            parameters[param.name] = value
        elif param.is_required and param.default_value:
            parameters[param.name] = param.default_value

    return parameters


def ensure_output_directory(execution_id):
    """
    确保输出目录存在
    """
    from django.conf import settings

    output_dir = os.path.join(
        settings.MEDIA_ROOT,
        settings.SCRIPT_OUTPUT_PATH,
        execution_id
    )

    os.makedirs(output_dir, exist_ok=True)
    return output_dir


def get_running_scripts_count():
    """
    获取当前正在运行的脚本数量
    """
    from .models import ExecutionRecord
    return ExecutionRecord.objects.filter(status__in=['pending', 'running']).count()


def can_execute_script():
    """
    检查是否可以执行新的脚本（基于并发限制）
    """
    from django.conf import settings

    running_count = get_running_scripts_count()
    max_concurrent = getattr(settings, 'MAX_CONCURRENT_SCRIPTS', 10)

    return running_count < max_concurrent


def format_file_size(size_bytes):
    """
    格式化文件大小显示
    """
    if size_bytes == 0:
        return "0 B"

    size_names = ["B", "KB", "MB", "GB", "TB"]
    import math
    i = int(math.floor(math.log(size_bytes, 1024)))
    p = math.pow(1024, i)
    s = round(size_bytes / p, 2)
    return f"{s} {size_names[i]}"


def get_script_execution_stats(script_id=None):
    """
    获取脚本执行统计信息
    """
    from .models import ExecutionRecord
    from django.db.models import Count, Q

    queryset = ExecutionRecord.objects.all()
    if script_id:
        queryset = queryset.filter(script_id=script_id)

    stats = queryset.aggregate(
        total=Count('id'),
        success=Count('id', filter=Q(status='success')),
        failed=Count('id', filter=Q(status='failed')),
        running=Count('id', filter=Q(status__in=['pending', 'running']))
    )

    stats['success_rate'] = (
        (stats['success'] / stats['total'] * 100)
        if stats['total'] > 0 else 0
    )

    return stats
