#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
示例脚本：数据处理和报告生成
这是一个演示脚本，展示如何处理输入参数并生成输出文件
"""

import argparse
import os
import sys
import pandas as pd
import json
from datetime import datetime


def main():
    parser = argparse.ArgumentParser(description='示例数据处理脚本')
    parser.add_argument('--input_file', type=str, help='输入文件路径')
    parser.add_argument('--output_format', type=str, default='csv', 
                       choices=['csv', 'excel', 'json'], help='输出格式')
    parser.add_argument('--filter_column', type=str, help='过滤列名')
    parser.add_argument('--filter_value', type=str, help='过滤值')
    parser.add_argument('--generate_report', action='store_true', help='生成HTML报告')
    
    args = parser.parse_args()
    
    print(f"开始执行脚本: {datetime.now()}")
    print(f"参数: {vars(args)}")
    
    # 创建输出目录
    output_dir = os.environ.get('SCRIPT_OUTPUT_DIR', './output')
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # 如果有输入文件，处理数据
        if args.input_file and os.path.exists(args.input_file):
            print(f"读取输入文件: {args.input_file}")
            
            # 根据文件扩展名读取数据
            if args.input_file.endswith('.csv'):
                df = pd.read_csv(args.input_file)
            elif args.input_file.endswith(('.xlsx', '.xls')):
                df = pd.read_excel(args.input_file)
            else:
                print("不支持的文件格式")
                return 1
            
            print(f"数据形状: {df.shape}")
            print(f"列名: {list(df.columns)}")
            
            # 应用过滤器
            if args.filter_column and args.filter_value:
                if args.filter_column in df.columns:
                    original_count = len(df)
                    df = df[df[args.filter_column].astype(str).str.contains(args.filter_value, na=False)]
                    print(f"过滤后数据: {len(df)} 行 (原始: {original_count} 行)")
                else:
                    print(f"警告: 列 '{args.filter_column}' 不存在")
            
            # 生成输出文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            if args.output_format == 'csv':
                output_file = os.path.join(output_dir, f'processed_data_{timestamp}.csv')
                df.to_csv(output_file, index=False, encoding='utf-8-sig')
                print(f"CSV文件已保存: {output_file}")
                
            elif args.output_format == 'excel':
                output_file = os.path.join(output_dir, f'processed_data_{timestamp}.xlsx')
                df.to_excel(output_file, index=False)
                print(f"Excel文件已保存: {output_file}")
                
            elif args.output_format == 'json':
                output_file = os.path.join(output_dir, f'processed_data_{timestamp}.json')
                df.to_json(output_file, orient='records', force_ascii=False, indent=2)
                print(f"JSON文件已保存: {output_file}")
            
            # 生成统计信息
            stats = {
                'total_rows': len(df),
                'total_columns': len(df.columns),
                'columns': list(df.columns),
                'numeric_columns': list(df.select_dtypes(include=['number']).columns),
                'processing_time': datetime.now().isoformat(),
                'filter_applied': bool(args.filter_column and args.filter_value)
            }
            
            stats_file = os.path.join(output_dir, f'stats_{timestamp}.json')
            with open(stats_file, 'w', encoding='utf-8') as f:
                json.dump(stats, f, ensure_ascii=False, indent=2)
            print(f"统计信息已保存: {stats_file}")
            
        else:
            # 没有输入文件时，生成示例数据
            print("生成示例数据...")
            import numpy as np
            
            data = {
                'ID': range(1, 101),
                'Name': [f'Item_{i}' for i in range(1, 101)],
                'Value': np.random.randint(1, 1000, 100),
                'Category': np.random.choice(['A', 'B', 'C'], 100),
                'Date': pd.date_range('2023-01-01', periods=100, freq='D')
            }
            
            df = pd.DataFrame(data)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            output_file = os.path.join(output_dir, f'sample_data_{timestamp}.csv')
            df.to_csv(output_file, index=False, encoding='utf-8-sig')
            print(f"示例数据已生成: {output_file}")
        
        # 生成HTML报告
        if args.generate_report:
            print("生成HTML报告...")
            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>数据处理报告</title>
                <meta charset="utf-8">
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    .header {{ background: #f0f0f0; padding: 10px; border-radius: 5px; }}
                    .stats {{ margin: 20px 0; }}
                    .stats table {{ border-collapse: collapse; width: 100%; }}
                    .stats th, .stats td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                    .stats th {{ background-color: #f2f2f2; }}
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>数据处理报告</h1>
                    <p>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                </div>
                
                <div class="stats">
                    <h2>处理统计</h2>
                    <table>
                        <tr><th>项目</th><th>值</th></tr>
                        <tr><td>数据行数</td><td>{len(df) if 'df' in locals() else 'N/A'}</td></tr>
                        <tr><td>数据列数</td><td>{len(df.columns) if 'df' in locals() else 'N/A'}</td></tr>
                        <tr><td>输出格式</td><td>{args.output_format}</td></tr>
                        <tr><td>是否应用过滤</td><td>{'是' if args.filter_column and args.filter_value else '否'}</td></tr>
                    </table>
                </div>
                
                {'<div class="data-preview"><h2>数据预览</h2>' + df.head().to_html() + '</div>' if 'df' in locals() else ''}
            </body>
            </html>
            """
            
            report_file = os.path.join(output_dir, f'report_{timestamp}.html')
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
            print(f"HTML报告已生成: {report_file}")
        
        print(f"脚本执行完成: {datetime.now()}")
        return 0
        
    except Exception as e:
        print(f"错误: {str(e)}", file=sys.stderr)
        return 1


if __name__ == '__main__':
    sys.exit(main())
