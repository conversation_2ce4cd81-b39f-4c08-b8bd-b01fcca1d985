from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.admin.views.decorators import staff_member_required
from django.contrib import messages
from django.http import JsonResponse, HttpResponse, Http404
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.views.generic import TemplateView
from django.conf import settings
from django.utils import timezone
import os
import json

from .models import Script, ExecutionRecord, ScheduledTask
from .tasks import execute_script_task
from .utils import (
    log_system_event,
    get_script_parameters_form_data,
    prepare_script_execution_parameters,
    can_execute_script,
    get_script_execution_stats
)


@staff_member_required
def execute_script_view(request, script_id):
    """
    执行脚本视图
    """
    script = get_object_or_404(Script, id=script_id)

    if request.method == 'GET':
        # 显示参数输入表单
        form_data = get_script_parameters_form_data(script)
        context = {
            'script': script,
            'form_data': form_data,
            'can_execute': can_execute_script()
        }
        return render(request, 'admin/script_manager/execute_script.html', context)

    elif request.method == 'POST':
        # 执行脚本
        if not can_execute_script():
            messages.error(request, '当前并发执行数已达上限，请稍后再试')
            return redirect('admin:script_manager_script_changelist')

        # 准备执行参数
        form_data = request.POST.dict()
        parameters = prepare_script_execution_parameters(script, form_data)

        # 执行脚本（如果Celery可用则异步执行，否则同步执行）
        try:
            from .tasks import CELERY_AVAILABLE
            if CELERY_AVAILABLE:
                task = execute_script_task.delay(
                    script_id=script.id,
                    parameters=parameters,
                    triggered_by_id=request.user.id
                )
                messages.success(request, f'脚本 "{script.name}" 已开始执行，任务ID: {task.id}')
            else:
                # 同步执行（仅用于测试）
                result = execute_script_task(
                    script_id=script.id,
                    parameters=parameters,
                    triggered_by_id=request.user.id
                )
                messages.success(request, f'脚本 "{script.name}" 执行完成，状态: {result.get("status", "unknown")}')
        except Exception as e:
            messages.error(request, f'脚本执行失败: {str(e)}')

        log_system_event('INFO', f'用户 {request.user.username} 手动执行脚本: {script.name}',
                        'script_execution', user_id=request.user.id)

        return redirect('admin:script_manager_executionrecord_changelist')


@staff_member_required
def view_execution_result(request, record_id):
    """
    查看执行结果视图
    """
    record = get_object_or_404(ExecutionRecord, id=record_id)

    # 获取HTML结果内容
    html_result_content = record.get_html_result_content()

    context = {
        'record': record,
        'stats': get_script_execution_stats(record.script.id),
        'output_files': record.get_output_files(),  # 使用模型方法获取输出文件
        'html_result_content': html_result_content,  # HTML结果内容
        'has_html_result': record.has_html_result(),  # 是否有HTML结果
    }

    return render(request, 'admin/script_manager/execution_result.html', context)


@staff_member_required
def download_output_file(request, record_id, file_name):
    """
    下载输出文件
    """
    import urllib.parse
    from django.http import FileResponse
    import mimetypes

    record = get_object_or_404(ExecutionRecord, id=record_id)

    # URL解码文件名
    decoded_file_name = urllib.parse.unquote(file_name)

    # 查找文件
    file_info = None
    output_files = record.get_output_files()
    for file_data in output_files:
        if file_data['name'] == decoded_file_name:
            file_info = file_data
            break

    if not file_info:
        raise Http404("文件不存在")

    file_path = os.path.join(settings.MEDIA_ROOT, file_info['path'])

    if not os.path.exists(file_path):
        raise Http404("文件不存在")

    # 获取MIME类型
    content_type, _ = mimetypes.guess_type(file_path)
    if content_type is None:
        content_type = 'application/octet-stream'

    # 确保文件名包含正确的扩展名
    original_filename = file_info['name']

    # 使用FileResponse提供更好的文件下载支持
    response = FileResponse(
        open(file_path, 'rb'),
        content_type=content_type,
        as_attachment=True,
        filename=original_filename
    )

    return response


@staff_member_required
@require_http_methods(["POST"])
def cancel_execution(request, record_id):
    """
    取消脚本执行
    """
    record = get_object_or_404(ExecutionRecord, id=record_id)

    if record.is_running:
        # 这里可以添加取消任务的逻辑
        # 例如使用Celery的revoke功能
        record.status = 'cancelled'
        record.finished_at = timezone.now()
        record.save()

        log_system_event('INFO', f'用户 {request.user.username} 取消了脚本执行: {record.execution_id}',
                        'script_execution', user_id=request.user.id)

        messages.success(request, '脚本执行已取消')
    else:
        messages.warning(request, '脚本已完成，无法取消')

    return redirect('admin:script_manager_executionrecord_changelist')


class DashboardView(TemplateView):
    """
    仪表板视图
    """
    template_name = 'admin/script_manager/dashboard.html'

    @method_decorator(staff_member_required)
    def dispatch(self, *args, **kwargs):
        return super().dispatch(*args, **kwargs)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # 获取统计数据
        from .models import Script, ExecutionRecord, ScheduledTask
        from django.db.models import Count, Q
        from datetime import datetime, timedelta

        # 基本统计
        context['total_scripts'] = Script.objects.filter(is_active=True).count()
        context['total_tasks'] = ScheduledTask.objects.filter(is_active=True).count()
        context['running_executions'] = ExecutionRecord.objects.filter(
            status__in=['pending', 'running']
        ).count()

        # 最近24小时执行统计
        yesterday = datetime.now() - timedelta(days=1)
        recent_executions = ExecutionRecord.objects.filter(created_at__gte=yesterday)
        context['recent_stats'] = recent_executions.aggregate(
            total=Count('id'),
            success=Count('id', filter=Q(status='success')),
            failed=Count('id', filter=Q(status='failed'))
        )

        # 最近执行记录
        context['recent_executions'] = ExecutionRecord.objects.select_related(
            'script', 'triggered_by'
        ).order_by('-created_at')[:10]

        return context
