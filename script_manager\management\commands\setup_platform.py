from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.core.files import File
from script_manager.models import Script, ScriptParameter
import os


class Command(BaseCommand):
    help = '初始化脚本平台，创建示例数据'

    def add_arguments(self, parser):
        parser.add_argument(
            '--create-superuser',
            action='store_true',
            help='创建超级用户',
        )
        parser.add_argument(
            '--username',
            type=str,
            default='admin',
            help='超级用户用户名 (默认: admin)',
        )
        parser.add_argument(
            '--password',
            type=str,
            default='admin123',
            help='超级用户密码 (默认: admin123)',
        )
        parser.add_argument(
            '--email',
            type=str,
            default='<EMAIL>',
            help='超级用户邮箱 (默认: <EMAIL>)',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('开始初始化脚本平台...'))

        # 创建超级用户
        if options['create_superuser']:
            self.create_superuser(options)

        # 创建示例脚本
        self.create_example_script()

        self.stdout.write(self.style.SUCCESS('平台初始化完成！'))

    def create_superuser(self, options):
        username = options['username']
        password = options['password']
        email = options['email']

        if User.objects.filter(username=username).exists():
            self.stdout.write(
                self.style.WARNING(f'用户 {username} 已存在，跳过创建')
            )
            return

        user = User.objects.create_superuser(
            username=username,
            email=email,
            password=password
        )
        self.stdout.write(
            self.style.SUCCESS(f'超级用户 {username} 创建成功')
        )

    def create_example_script(self):
        # 检查示例脚本是否已存在
        if Script.objects.filter(name='示例数据处理脚本').exists():
            self.stdout.write(
                self.style.WARNING('示例脚本已存在，跳过创建')
            )
            return

        # 获取或创建默认用户
        admin_user = User.objects.filter(is_superuser=True).first()
        if not admin_user:
            admin_user = User.objects.create_user(
                username='system',
                email='<EMAIL>',
                password='system123'
            )

        # 创建示例脚本
        script_path = 'media/scripts/example_script.py'
        if os.path.exists(script_path):
            script = Script.objects.create(
                name='示例数据处理脚本',
                description='这是一个示例脚本，演示如何处理CSV/Excel文件并生成报告。支持数据过滤、格式转换和HTML报告生成。',
                owner=admin_user,
                is_active=True
            )

            # 手动设置文件路径（因为我们已经有了文件）
            script.file.name = 'scripts/example_script.py'
            script.save()

            # 创建脚本参数
            parameters = [
                {
                    'name': 'input_file',
                    'display_name': '输入文件',
                    'parameter_type': 'file',
                    'is_required': False,
                    'help_text': '要处理的CSV或Excel文件',
                    'order': 1
                },
                {
                    'name': 'output_format',
                    'display_name': '输出格式',
                    'parameter_type': 'select',
                    'is_required': True,
                    'default_value': 'csv',
                    'choices': 'csv\nexcel\njson',
                    'help_text': '选择输出文件格式',
                    'order': 2
                },
                {
                    'name': 'filter_column',
                    'display_name': '过滤列名',
                    'parameter_type': 'text',
                    'is_required': False,
                    'help_text': '要过滤的列名',
                    'order': 3
                },
                {
                    'name': 'filter_value',
                    'display_name': '过滤值',
                    'parameter_type': 'text',
                    'is_required': False,
                    'help_text': '过滤条件的值',
                    'order': 4
                },
                {
                    'name': 'generate_report',
                    'display_name': '生成HTML报告',
                    'parameter_type': 'boolean',
                    'is_required': False,
                    'default_value': 'true',
                    'help_text': '是否生成HTML格式的处理报告',
                    'order': 5
                }
            ]

            for param_data in parameters:
                ScriptParameter.objects.create(
                    script=script,
                    **param_data
                )

            self.stdout.write(
                self.style.SUCCESS(f'示例脚本 "{script.name}" 创建成功')
            )
        else:
            self.stdout.write(
                self.style.ERROR(f'示例脚本文件不存在: {script_path}')
            )
