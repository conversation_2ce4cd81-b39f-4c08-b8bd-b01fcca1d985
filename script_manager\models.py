from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
import os
import json


class Script(models.Model):
    """脚本模型"""
    name = models.CharField(max_length=200, verbose_name='脚本名称')
    description = models.TextField(blank=True, verbose_name='脚本描述')
    file = models.FileField(upload_to='scripts/', verbose_name='脚本文件')
    owner = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='负责人')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    is_active = models.BooleanField(default=True, verbose_name='是否启用')

    class Meta:
        verbose_name = '脚本'
        verbose_name_plural = '脚本管理'
        ordering = ['-created_at']

    def __str__(self):
        return self.name

    def get_file_path(self):
        """获取脚本文件的绝对路径"""
        return self.file.path if self.file else None

    def get_script_folder(self):
        """获取脚本专用文件夹路径"""
        from django.conf import settings
        if getattr(settings, 'SCRIPT_INDIVIDUAL_FOLDERS', False):
            # 使用脚本ID创建独立文件夹
            folder_name = f"script_{self.id}_{self.name}"
            # 清理文件夹名称中的特殊字符
            import re
            folder_name = re.sub(r'[^\w\-_.]', '_', folder_name)
            return os.path.join(settings.MEDIA_ROOT, settings.SCRIPT_UPLOAD_PATH, folder_name)
        else:
            return os.path.join(settings.MEDIA_ROOT, settings.SCRIPT_UPLOAD_PATH)

    def get_execution_output_folder(self, execution_id):
        """获取特定执行的输出文件夹路径"""
        from django.conf import settings
        script_folder = self.get_script_folder()
        execution_subfolder = getattr(settings, 'SCRIPT_EXECUTION_SUBFOLDER', 'executions')
        return os.path.join(script_folder, execution_subfolder, execution_id)


class ScriptParameter(models.Model):
    """脚本参数模型"""
    PARAMETER_TYPES = [
        ('text', '文本'),
        ('number', '数字'),
        ('boolean', '布尔值'),
        ('file', '文件'),
        ('select', '选择'),
    ]

    script = models.ForeignKey(Script, on_delete=models.CASCADE, related_name='parameters', verbose_name='所属脚本')
    name = models.CharField(max_length=100, verbose_name='参数名')
    display_name = models.CharField(max_length=100, verbose_name='显示名称')
    parameter_type = models.CharField(max_length=20, choices=PARAMETER_TYPES, verbose_name='参数类型')
    is_required = models.BooleanField(default=True, verbose_name='是否必填')
    default_value = models.TextField(blank=True, verbose_name='默认值')
    choices = models.TextField(blank=True, help_text='选择类型的选项，每行一个', verbose_name='选项')
    help_text = models.TextField(blank=True, verbose_name='帮助文本')
    order = models.IntegerField(default=0, verbose_name='排序')

    class Meta:
        verbose_name = '脚本参数'
        verbose_name_plural = '脚本参数'
        ordering = ['order', 'id']

    def __str__(self):
        return f"{self.script.name} - {self.display_name}"


class ScheduledTask(models.Model):
    """定时任务模型"""
    name = models.CharField(max_length=200, verbose_name='任务名称')
    script = models.ForeignKey(Script, on_delete=models.CASCADE, verbose_name='执行脚本')
    cron_expression = models.CharField(max_length=100, verbose_name='Cron表达式')
    parameters = models.TextField(default='{}', blank=True, verbose_name='执行参数')
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='创建者')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    last_run_at = models.DateTimeField(null=True, blank=True, verbose_name='最后执行时间')
    next_run_at = models.DateTimeField(null=True, blank=True, verbose_name='下次执行时间')

    class Meta:
        verbose_name = '定时任务'
        verbose_name_plural = '任务调度'
        ordering = ['-created_at']

    def __str__(self):
        return self.name

    def get_parameters(self):
        """获取参数字典"""
        try:
            return json.loads(self.parameters) if self.parameters else {}
        except json.JSONDecodeError:
            return {}

    def set_parameters(self, params_dict):
        """设置参数字典"""
        self.parameters = json.dumps(params_dict, ensure_ascii=False)


class ExecutionRecord(models.Model):
    """脚本执行记录模型"""
    STATUS_CHOICES = [
        ('pending', '等待中'),
        ('running', '运行中'),
        ('success', '成功'),
        ('failed', '失败'),
        ('cancelled', '已取消'),
    ]

    TRIGGER_TYPES = [
        ('manual', '手动执行'),
        ('scheduled', '定时执行'),
    ]

    script = models.ForeignKey(Script, on_delete=models.CASCADE, verbose_name='执行脚本')
    scheduled_task = models.ForeignKey(ScheduledTask, on_delete=models.SET_NULL, null=True, blank=True, verbose_name='关联定时任务')
    execution_id = models.CharField(max_length=100, unique=True, verbose_name='执行ID')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', verbose_name='执行状态')
    trigger_type = models.CharField(max_length=20, choices=TRIGGER_TYPES, verbose_name='触发方式')
    triggered_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, verbose_name='触发者')
    parameters = models.TextField(default='{}', blank=True, verbose_name='执行参数')

    # 执行时间相关
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    started_at = models.DateTimeField(null=True, blank=True, verbose_name='开始时间')
    finished_at = models.DateTimeField(null=True, blank=True, verbose_name='结束时间')

    # 执行结果
    stdout = models.TextField(blank=True, verbose_name='标准输出')
    stderr = models.TextField(blank=True, verbose_name='错误输出')
    return_code = models.IntegerField(null=True, blank=True, verbose_name='返回码')
    output_files = models.TextField(default='[]', blank=True, verbose_name='输出文件')

    class Meta:
        verbose_name = '执行记录'
        verbose_name_plural = '执行记录'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.script.name} - {self.execution_id}"

    @property
    def duration(self):
        """计算执行时长"""
        if self.started_at and self.finished_at:
            return self.finished_at - self.started_at
        return None

    @property
    def is_running(self):
        """判断是否正在运行"""
        return self.status in ['pending', 'running']

    def get_parameters(self):
        """获取参数字典"""
        try:
            return json.loads(self.parameters) if self.parameters else {}
        except json.JSONDecodeError:
            return {}

    def set_parameters(self, params_dict):
        """设置参数字典"""
        self.parameters = json.dumps(params_dict, ensure_ascii=False)

    def get_output_files(self):
        """获取输出文件列表"""
        try:
            return json.loads(self.output_files) if self.output_files else []
        except json.JSONDecodeError:
            return []

    def set_output_files(self, files_list):
        """设置输出文件列表"""
        self.output_files = json.dumps(files_list, ensure_ascii=False)

    def get_html_result_content(self):
        """获取HTML结果文件内容"""
        from django.conf import settings
        html_filename = getattr(settings, 'REQUIRED_HTML_RESULT_FILE', '结果正文.html')

        # 查找HTML结果文件
        output_files = self.get_output_files()
        html_file_path = None

        for file_info in output_files:
            if file_info.get('name') == html_filename:
                html_file_path = os.path.join(settings.MEDIA_ROOT, file_info.get('path', ''))
                break

        if html_file_path and os.path.exists(html_file_path):
            try:
                with open(html_file_path, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                    return content if content else None
            except Exception as e:
                return f"读取HTML文件时出错: {str(e)}"

        return None

    def has_html_result(self):
        """检查是否有HTML结果文件"""
        return self.get_html_result_content() is not None


class SystemLog(models.Model):
    """系统日志模型"""
    LOG_LEVELS = [
        ('DEBUG', 'Debug'),
        ('INFO', 'Info'),
        ('WARNING', 'Warning'),
        ('ERROR', 'Error'),
        ('CRITICAL', 'Critical'),
    ]

    level = models.CharField(max_length=20, choices=LOG_LEVELS, verbose_name='日志级别')
    message = models.TextField(verbose_name='日志消息')
    module = models.CharField(max_length=100, verbose_name='模块')
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, verbose_name='用户')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    extra_data = models.TextField(default='{}', blank=True, verbose_name='额外数据')

    class Meta:
        verbose_name = '系统日志'
        verbose_name_plural = '系统日志'
        ordering = ['-created_at']

    def __str__(self):
        return f"[{self.level}] {self.message[:50]}"

    def get_extra_data(self):
        """获取额外数据字典"""
        try:
            return json.loads(self.extra_data) if self.extra_data else {}
        except json.JSONDecodeError:
            return {}

    def set_extra_data(self, data_dict):
        """设置额外数据字典"""
        self.extra_data = json.dumps(data_dict, ensure_ascii=False)