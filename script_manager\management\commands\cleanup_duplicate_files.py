#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
清理重复文件的管理命令
"""

import os
import shutil
from django.core.management.base import BaseCommand
from django.conf import settings


class Command(BaseCommand):
    help = '清理media/scripts/目录下的重复文件，只保留脚本专用文件夹中的文件'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='只显示将要删除的文件，不实际删除',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='强制删除，不询问确认',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        force = options['force']
        
        scripts_dir = os.path.join(settings.MEDIA_ROOT, settings.SCRIPT_UPLOAD_PATH)
        
        if not os.path.exists(scripts_dir):
            self.stdout.write(
                self.style.WARNING(f'脚本目录不存在: {scripts_dir}')
            )
            return
        
        # 收集要删除的文件
        files_to_delete = []
        script_folders = []
        
        for item in os.listdir(scripts_dir):
            item_path = os.path.join(scripts_dir, item)
            
            if os.path.isfile(item_path):
                # 根目录下的文件（除了.gitkeep等系统文件）
                if not item.startswith('.'):
                    files_to_delete.append(item_path)
            elif os.path.isdir(item_path) and item.startswith('script_'):
                # 脚本专用文件夹
                script_folders.append(item_path)
        
        if not files_to_delete:
            self.stdout.write(
                self.style.SUCCESS('没有发现需要清理的重复文件')
            )
            return
        
        # 显示将要删除的文件
        self.stdout.write(
            self.style.WARNING(f'发现 {len(files_to_delete)} 个重复文件:')
        )
        
        for file_path in files_to_delete:
            file_name = os.path.basename(file_path)
            file_size = os.path.getsize(file_path)
            self.stdout.write(f'  - {file_name} ({file_size} bytes)')
        
        self.stdout.write(
            self.style.SUCCESS(f'发现 {len(script_folders)} 个脚本专用文件夹:')
        )
        
        for folder_path in script_folders:
            folder_name = os.path.basename(folder_path)
            self.stdout.write(f'  - {folder_name}/')
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('这是预览模式，没有实际删除文件。使用 --force 参数执行实际删除。')
            )
            return
        
        # 确认删除
        if not force:
            confirm = input('\n确定要删除这些重复文件吗？(y/N): ')
            if confirm.lower() != 'y':
                self.stdout.write(
                    self.style.WARNING('操作已取消')
                )
                return
        
        # 执行删除
        deleted_count = 0
        for file_path in files_to_delete:
            try:
                os.remove(file_path)
                deleted_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'已删除: {os.path.basename(file_path)}')
                )
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'删除失败 {os.path.basename(file_path)}: {str(e)}')
                )
        
        self.stdout.write(
            self.style.SUCCESS(f'\n清理完成！共删除 {deleted_count} 个重复文件。')
        )
        
        # 显示清理后的目录结构
        self.stdout.write(
            self.style.SUCCESS('\n清理后的目录结构:')
        )
        self._show_directory_structure(scripts_dir)
    
    def _show_directory_structure(self, directory, prefix=''):
        """显示目录结构"""
        try:
            items = sorted(os.listdir(directory))
            for i, item in enumerate(items):
                item_path = os.path.join(directory, item)
                is_last = i == len(items) - 1
                current_prefix = '└── ' if is_last else '├── '
                
                if os.path.isdir(item_path):
                    self.stdout.write(f'{prefix}{current_prefix}{item}/')
                    # 只显示两级目录
                    if prefix.count('│') < 2:
                        next_prefix = prefix + ('    ' if is_last else '│   ')
                        self._show_directory_structure(item_path, next_prefix)
                else:
                    file_size = os.path.getsize(item_path)
                    self.stdout.write(f'{prefix}{current_prefix}{item} ({file_size} bytes)')
        except PermissionError:
            self.stdout.write(f'{prefix}[权限不足]')
