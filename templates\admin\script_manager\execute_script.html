{% extends "admin/base_site.html" %}
{% load i18n admin_urls static admin_modify %}

{% block title %}执行脚本: {{ script.name }}{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
    <a href="{% url 'admin:index' %}">首页</a>
    &rsaquo; <a href="{% url 'admin:script_manager_script_changelist' %}">脚本管理</a>
    &rsaquo; 执行脚本: {{ script.name }}
</div>
{% endblock %}

{% block content %}
<div class="module aligned">
    <h1>执行脚本: {{ script.name }}</h1>
    
    {% if script.description %}
    <div class="form-row">
        <div class="field-box">
            <label>脚本描述:</label>
            <p>{{ script.description }}</p>
        </div>
    </div>
    {% endif %}
    
    {% if not can_execute %}
    <div class="messagelist">
        <div class="error">当前并发执行数已达上限，请稍后再试</div>
    </div>
    {% endif %}
    
    <form method="post" enctype="multipart/form-data">
        {% csrf_token %}
        
        {% if form_data %}
        <fieldset class="module aligned">
            <h2>脚本参数</h2>
            {% for field in form_data %}
            <div class="form-row">
                <div class="field-box">
                    <label for="id_{{ field.name }}" 
                           {% if field.required %}class="required"{% endif %}>
                        {{ field.display_name }}:
                    </label>
                    
                    {% if field.type == 'text' %}
                        <input type="text" name="{{ field.name }}" id="id_{{ field.name }}" 
                               value="{{ field.default_value }}" 
                               {% if field.required %}required{% endif %}>
                    {% elif field.type == 'number' %}
                        <input type="number" name="{{ field.name }}" id="id_{{ field.name }}" 
                               value="{{ field.default_value }}" 
                               {% if field.required %}required{% endif %}>
                    {% elif field.type == 'boolean' %}
                        <input type="checkbox" name="{{ field.name }}" id="id_{{ field.name }}" 
                               {% if field.default_value %}checked{% endif %}>
                    {% elif field.type == 'file' %}
                        <input type="file" name="{{ field.name }}" id="id_{{ field.name }}" 
                               {% if field.required %}required{% endif %}>
                    {% elif field.type == 'select' %}
                        <select name="{{ field.name }}" id="id_{{ field.name }}" 
                                {% if field.required %}required{% endif %}>
                            {% if not field.required %}
                                <option value="">---------</option>
                            {% endif %}
                            {% for choice in field.choices %}
                                <option value="{{ choice }}" 
                                        {% if choice == field.default_value %}selected{% endif %}>
                                    {{ choice }}
                                </option>
                            {% endfor %}
                        </select>
                    {% endif %}
                    
                    {% if field.help_text %}
                        <p class="help">{{ field.help_text }}</p>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </fieldset>
        {% endif %}
        
        <div class="submit-row">
            <input type="submit" value="执行脚本" class="default" 
                   {% if not can_execute %}disabled{% endif %}>
            <a href="{% url 'admin:script_manager_script_changelist' %}" class="button cancel-link">取消</a>
        </div>
    </form>
</div>
{% endblock %}
