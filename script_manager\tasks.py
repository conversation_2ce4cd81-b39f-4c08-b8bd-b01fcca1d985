import os
import subprocess
import uuid
import json
from datetime import datetime
try:
    from celery import shared_task
    CELERY_AVAILABLE = True
except ImportError:
    # Celery不可用时的装饰器替代
    def shared_task(func):
        return func
    CELERY_AVAILABLE = False

from django.conf import settings
from django.utils import timezone
from .models import Script, ExecutionRecord, SystemLog
from .utils import log_system_event


@shared_task
def execute_script_task(script_id, parameters=None, triggered_by_id=None, scheduled_task_id=None):
    """
    异步执行脚本任务
    """
    try:
        script = Script.objects.get(id=script_id)
        execution_id = str(uuid.uuid4())

        # 创建执行记录
        execution_record = ExecutionRecord.objects.create(
            script=script,
            execution_id=execution_id,
            status='pending',
            trigger_type='scheduled' if scheduled_task_id else 'manual',
            triggered_by_id=triggered_by_id,
            scheduled_task_id=scheduled_task_id,
        )
        execution_record.set_parameters(parameters or {})
        execution_record.save()

        log_system_event('INFO', f'开始执行脚本: {script.name}', 'script_execution',
                        user_id=triggered_by_id, extra_data={'execution_id': execution_id})

        # 更新状态为运行中
        execution_record.status = 'running'
        execution_record.started_at = timezone.now()
        execution_record.save()

        # 准备执行环境
        script_path = script.get_file_path()
        if not script_path or not os.path.exists(script_path):
            raise Exception(f"脚本文件不存在: {script_path}")

        # 构建命令行参数
        cmd = ['python', script_path]
        env = os.environ.copy()

        # 处理参数
        if parameters:
            for param_name, param_value in parameters.items():
                if param_value is not None:
                    cmd.extend([f'--{param_name}', str(param_value)])

        # 创建脚本专用的输出目录
        output_dir = script.get_execution_output_folder(execution_id)
        os.makedirs(output_dir, exist_ok=True)

        # 设置工作目录为输出目录，避免文件重复
        work_dir = output_dir

        # 设置环境变量，让脚本知道输出目录
        env['SCRIPT_OUTPUT_DIR'] = output_dir
        env['EXECUTION_ID'] = execution_id
        env['SCRIPT_WORK_DIR'] = work_dir

        # 执行脚本
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd=work_dir,
            env=env
        )

        stdout, stderr = process.communicate(timeout=settings.SCRIPT_EXECUTION_TIMEOUT)
        return_code = process.returncode

        # 更新执行记录
        execution_record.status = 'success' if return_code == 0 else 'failed'
        execution_record.finished_at = timezone.now()
        execution_record.stdout = stdout
        execution_record.stderr = stderr
        execution_record.return_code = return_code

        # 检查输出文件 - 简化的文件检测逻辑（避免重复）
        output_files = []

        # 只检查输出目录中的文件（脚本直接在此目录工作）
        if os.path.exists(output_dir):
            execution_start_time = execution_record.started_at.timestamp()

            for file_name in os.listdir(output_dir):
                file_path = os.path.join(output_dir, file_name)
                if os.path.isfile(file_path):
                    # 检查文件是否在执行期间生成
                    file_mtime = os.path.getmtime(file_path)
                    if file_mtime >= execution_start_time:
                        # 计算相对于MEDIA_ROOT的路径
                        relative_path = os.path.relpath(file_path, settings.MEDIA_ROOT)
                        output_files.append({
                            'name': file_name,
                            'path': relative_path,
                            'size': os.path.getsize(file_path),
                            'type': 'output'  # 标记为输出文件
                        })

        execution_record.set_output_files(output_files)
        execution_record.save()

        status_msg = '成功' if return_code == 0 else '失败'
        log_system_event('INFO', f'脚本执行{status_msg}: {script.name}', 'script_execution',
                        user_id=triggered_by_id, extra_data={
                            'execution_id': execution_id,
                            'return_code': return_code,
                            'duration': str(execution_record.duration)
                        })

        return {
            'execution_id': execution_id,
            'status': execution_record.status,
            'return_code': return_code
        }

    except subprocess.TimeoutExpired:
        execution_record.status = 'failed'
        execution_record.finished_at = timezone.now()
        execution_record.stderr = '脚本执行超时'
        execution_record.save()

        log_system_event('ERROR', f'脚本执行超时: {script.name}', 'script_execution',
                        user_id=triggered_by_id, extra_data={'execution_id': execution_id})

        return {
            'execution_id': execution_id,
            'status': 'failed',
            'error': '执行超时'
        }

    except Exception as e:
        execution_record.status = 'failed'
        execution_record.finished_at = timezone.now()
        execution_record.stderr = str(e)
        execution_record.save()

        log_system_event('ERROR', f'脚本执行异常: {script.name} - {str(e)}', 'script_execution',
                        user_id=triggered_by_id, extra_data={'execution_id': execution_id})

        return {
            'execution_id': execution_id,
            'status': 'failed',
            'error': str(e)
        }


@shared_task
def cleanup_old_execution_records():
    """
    清理旧的执行记录
    """
    from datetime import timedelta

    # 删除30天前的执行记录
    cutoff_date = timezone.now() - timedelta(days=30)
    old_records = ExecutionRecord.objects.filter(created_at__lt=cutoff_date)
    count = old_records.count()
    old_records.delete()

    log_system_event('INFO', f'清理了 {count} 条旧执行记录', 'system_maintenance')

    return f'清理了 {count} 条旧执行记录'


@shared_task
def cleanup_old_system_logs():
    """
    清理旧的系统日志
    """
    from datetime import timedelta

    # 删除7天前的系统日志
    cutoff_date = timezone.now() - timedelta(days=7)
    old_logs = SystemLog.objects.filter(created_at__lt=cutoff_date)
    count = old_logs.count()
    old_logs.delete()

    return f'清理了 {count} 条旧系统日志'
