from pysnmp.hlapi import *
import csv
from datetime import datetime
import concurrent.futures
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.application import MIMEApplication
from collections import defaultdict

# 设备列表（保持原始数据）
DEVICES = [
    # H3C 设备
    {'name': 'GZ-ZXJF-3L-3-6-Z-S6890-02', 'ip': '*************', 'vendor': 'H3C', 'location': '广州中信机房'},
    # {'name': 'GZ-ZXJF-3L-3-5-V-S9850-01', 'ip': '************', 'vendor': 'H3C', 'location': '广州中信机房'},
    # {'name': 'GZ-YT-3L-1-NI1-X-S6800-01', 'ip': '*************', 'vendor': 'H3C', 'location': '广州亚太机房'},
    # {'name': 'GZ-YT-3L-1-SI4-X-S6800-02', 'ip': '*************', 'vendor': 'H3C', 'location': '广州亚太机房'},
    # # {'name': 'GZ-YT-3L-1-NI1-V-S6800-01', 'ip': '************', 'vendor': 'H3C', 'location': '广州亚太机房'},  # SNMP不通
    # {'name': 'GZ-YT-3L-1-NI3-V-S6800-02', 'ip': '************', 'vendor': 'H3C', 'location': '广州亚太机房'},
    # {'name': 'GZ-DTJF-1-AP2-06-V-S9850-01', 'ip': '************', 'vendor': 'H3C', 'location': '广州电通机房'},
    # {'name': 'GZ-DTJF-1-AP2-08-V-S9850-02', 'ip': '************', 'vendor': 'H3C', 'location': '广州电通机房'},
    # {'name': 'GZ-DTJF-2L-J3-7B17-V-S6890-01', 'ip': '***********52', 'vendor': 'H3C', 'location': '广州电通机房'},
    # {'name': 'GZ-DTJF-2L-J3-7B17-V-S6890-02', 'ip': '************', 'vendor': 'H3C', 'location': '广州电通机房'},
    # {'name': 'GZ-HXY-6L-B14-Z-S6890-01', 'ip': '*************', 'vendor': 'H3C', 'location': '广州华新园机房'},
    # {'name': 'GZ-HXY-6L-C6-Z-S6890-02', 'ip': '*************', 'vendor': 'H3C', 'location': '广州华新园机房'},
    # {'name': 'GZ-HXY-6L-C6-V-S9850-01', 'ip': '***********50', 'vendor': 'H3C', 'location': '广州华新园机房'},
    # {'name': 'GZ-HXY-6L-C6-V-S9850-02', 'ip': '*************', 'vendor': 'H3C', 'location': '广州华新园机房'},
    # {'name': 'GZ-NX-304-D16-Z-S6890-01', 'ip': '*************', 'vendor': 'H3C', 'location': '广州南翔机房'},
    # {'name': 'GZ-NX-304-D16-Z-S6890-02', 'ip': '*************', 'vendor': 'H3C', 'location': '广州南翔机房'},
    # {'name': 'GZ-NX-304-D16-V-S9850-01', 'ip': '************', 'vendor': 'H3C', 'location': '广州南翔机房'},
    # {'name': 'GZ-NX-304-D16-V-S9850-02', 'ip': '*************', 'vendor': 'H3C', 'location': '广州南翔机房'},
    # {'name': 'GZ-GZL405-A03-Z-S6520-01', 'ip': '************', 'vendor': 'H3C', 'location': '广州广之旅机房'},
    # {'name': 'GZ-GZL-405-A03-V-S9850-01', 'ip': '***********44', 'vendor': 'H3C', 'location': '广州广之旅机房'},
    # {'name': 'GZ-GZL-405-A02-V-S9850-02', 'ip': '*************', 'vendor': 'H3C', 'location': '广州广之旅机房'},
    # {'name': 'GZ-GDLJF-105-101-Z-S6890-01', 'ip': '************', 'vendor': 'H3C', 'location': '广州云泰机房'},
    # {'name': 'GZ-GDLJF-105-101-V-S6890-02', 'ip': '************', 'vendor': 'H3C', 'location': '广州云泰机房'},
    # {'name': 'GZ-GDLJF-105-101-Z-S6890-01', 'ip': '**************', 'vendor': 'H3C', 'location': '广州云泰机房'},
    # {'name': 'GZ-GDLJF-105-101-Z-S6890-02', 'ip': '**************', 'vendor': 'H3C', 'location': '广州云泰机房'},
    # {'name': 'GZ-QR-4L-403-G01-V-S6890-01', 'ip': '***********40', 'vendor': 'H3C', 'location': '广州旗锐机房'},
    # {'name': 'GZ-QR-4L-403-G01-V-S6890-02', 'ip': '*************', 'vendor': 'H3C', 'location': '广州旗锐机房'},
    # {'name': 'GZ-QR-4L-403-G01-X-S6800-01', 'ip': '**************', 'vendor': 'H3C', 'location': '广州旗锐机房'},
    # {'name': 'GZ-QR-4L-403-G01-X-S6800-02', 'ip': '**************', 'vendor': 'H3C', 'location': '广州旗锐机房'},
    # {'name': 'GZ-QR-4L-403-G01-Z-S6520-01', 'ip': '*************', 'vendor': 'H3C', 'location': '广州旗锐机房'},
    # {'name': 'GZ-QR-4L-403-G01-Z-S6520-02', 'ip': '*************', 'vendor': 'H3C', 'location': '广州旗锐机房'},
    # {'name': 'GZ-LY-3L-1-0113-V-S9850-01', 'ip': '************', 'vendor': 'H3C', 'location': '广州连云1期机房'},
    # {'name': 'GZ-LY-3L-1-0114-V-S9850-02', 'ip': '***********43', 'vendor': 'H3C', 'location': '广州连云1期机房'},
    # {'name': 'GZ-LY-3L-1-0113-X-S9850-03', 'ip': '*************', 'vendor': 'H3C', 'location': '广州连云1期机房'},
    # {'name': 'GZ-LY-3L-1-0114-X-S9850-04', 'ip': '*************', 'vendor': 'H3C', 'location': '广州连云1期机房'},
    # {'name': 'GZ-LY-3L-1-0114-V-S6800-06', 'ip': '************', 'vendor': 'H3C', 'location': '广州连云1期机房'},
    # {'name': 'GZ-LY-4L-1-0114-V-S6890-01', 'ip': '***********51', 'vendor': 'H3C', 'location': '广州连云1期机房'},
    # {'name': 'GZ-LY-4L-1-0114-V-S6890-02', 'ip': '************', 'vendor': 'H3C', 'location': '广州连云1期机房'},
    # {'name': 'GZ-LY2-403-1402-V-S9850-01', 'ip': '***********48', 'vendor': 'H3C', 'location': '广州连云2期机房'},
    # {'name': 'GZ-LY2-403-1403-V-S9850-02', 'ip': '***********49', 'vendor': 'H3C', 'location': '广州连云2期机房'},
    # {'name': 'GZ-LY3-305-B02-L2C-S6800-01', 'ip': '*************', 'vendor': 'H3C', 'location': '广州连云3期机房'},
    # {'name': 'GZ-LY3-305-B02-V-S9850-01', 'ip': '*************', 'vendor': 'H3C', 'location': '广州连云3期机房'},
    # {'name': 'GZ-LY3-305-B03-V-S9850-02', 'ip': '************', 'vendor': 'H3C', 'location': '广州连云3期机房'},
    # {'name': 'GZ-LY3-305-B02-X-S9850-03', 'ip': '***********82', 'vendor': 'H3C', 'location': '广州连云3期机房'},
    # {'name': 'GZ-LY3-305-B03-X-S9850-04', 'ip': '***********81', 'vendor': 'H3C', 'location': '广州连云3期机房'},
    # {'name': 'GZ-LY1-302-1205-S6805-Traffic-SW-A', 'ip': '*************', 'vendor': 'H3C', 'location': '广州连云1期机房'},
    # {'name': 'GZ-LY1-302-1205-S6805-Traffic-SW-B', 'ip': '**************', 'vendor': 'H3C', 'location': '广州连云1期机房'},
    # {'name': 'GZ-LY1-302-1205-S6805-Traffic-SW-C', 'ip': '***********', 'vendor': 'H3C', 'location': '广州连云1期机房'},
    # {'name': 'FS-TC-5L-502-V-S6890-01', 'ip': '************', 'vendor': 'H3C', 'location': '佛山天辰机房'},
    # {'name': 'FS-TC-5L-502-V-S6890-02', 'ip': '************', 'vendor': 'H3C', 'location': '佛山天辰机房'},
    # {'name': 'FS-TC-5L-502-Z-S6800-01', 'ip': '************', 'vendor': 'H3C', 'location': '佛山天辰机房'},
    # {'name': 'FS-ZHXC-3L-HH08-04-V-S9850-01', 'ip': '***********41', 'vendor': 'H3C', 'location': '佛山智慧新城机房'},
    # {'name': 'FS-ZHXC-3L-HH08-05-V-S9850-02', 'ip': '***********42', 'vendor': 'H3C', 'location': '佛山智慧新城机房'},
    # {'name': 'FS-ZHXC-3L-HH08-04-X-S9850-03', 'ip': '*************', 'vendor': 'H3C', 'location': '佛山智慧新城机房'},
    # {'name': 'FS-ZHXC-3L-HH08-05-X-S9850-04', 'ip': '*************', 'vendor': 'H3C', 'location': '佛山智慧新城机房'},
    # {'name': 'FS-FN-202-0514-V-S6890-01', 'ip': '************', 'vendor': 'H3C', 'location': '佛山福能机房'},
    # {'name': 'FS-FN-202-0514-V-S6890-02', 'ip': '************', 'vendor': 'H3C', 'location': '佛山福能机房'},
    # {'name': 'FS-FN-202-0514-Z-S6890-03', 'ip': '***********', 'vendor': 'H3C', 'location': '佛山福能机房'},
    # {'name': 'FS-FN-202-0514-Z-S6890-04', 'ip': '*************', 'vendor': 'H3C', 'location': '佛山福能机房'},
    # {'name': 'FS_ShunDe_4F_A_02_03_S6800_01', 'ip': '**************', 'vendor': 'H3C', 'location': '佛山顺德机房'},
    # {'name': 'FS_ShunDe_4F_A_02_03_S6800_02', 'ip': '*************', 'vendor': 'H3C', 'location': '佛山顺德机房'},
    # {'name': 'FS_ShunDe_4F_A_02_03_S6800_02', 'ip': '*************', 'vendor': 'H3C', 'location': '佛山顺德机房'},
    # {'name': 'SZ-ZXJF-3L-M00-V-S6890-01', 'ip': '************', 'vendor': 'H3C', 'location': '深圳中信机房'},
    # {'name': 'SZ-ZSJF-ZH02-V-S9850-01', 'ip': '************', 'vendor': 'H3C', 'location': '深圳中深机房'},
    # {'name': 'SZ-RJJD-4L-NW07-CNIX-S6890-01', 'ip': '**************', 'vendor': 'H3C', 'location': '深圳软基机房'},
    # {'name': 'SZ-BH-9L-901-B01-Z-S6805-01', 'ip': '************', 'vendor': 'H3C', 'location': '深圳博浩机房'},
    # {'name': 'SZ-BWX-1L-A09-V-S6890-01', 'ip': '************', 'vendor': 'H3C', 'location': '深圳百旺信机房'},
    # {'name': 'SZ-BWX-1L-A09-V-S6890-02', 'ip': '************', 'vendor': 'H3C', 'location': '深圳百旺信机房'},
    # {'name': 'SZ-BWX-1L-A09-Z-S6800-01', 'ip': '************', 'vendor': 'H3C', 'location': '深圳百旺信机房'},
    # {'name': 'SZ-BWX-1L-A09-Z-S6800-02', 'ip': '************', 'vendor': 'H3C', 'location': '深圳百旺信机房'},
    # {'name': 'SZ-HYC-1L-1-051-V-S6800-01', 'ip': '************', 'vendor': 'H3C', 'location': '深圳花园城机房'},
    # {'name': 'SZ-HYC-1L-1-052-V-S6800-02', 'ip': '************', 'vendor': 'H3C', 'location': '深圳花园城机房'},
    # {'name': 'SZ-RJJD-4L-NW06-V-S6800-01', 'ip': '************', 'vendor': 'H3C', 'location': '深圳软基机房'},
    # {'name': 'SZ-RJJD-4L-NW06-V-S6800-02', 'ip': '************', 'vendor': 'H3C', 'location': '深圳软基机房'},
    # {'name': 'SZ-RJJD-4L-NW07-V-S9850-01', 'ip': '***********46', 'vendor': 'H3C', 'location': '深圳软基机房'},
    # {'name': 'SZ-RJJD-4L-NW06-V-S9850-02', 'ip': '***********47', 'vendor': 'H3C', 'location': '深圳软基机房'},
    # {'name': 'SZ-RJJD-4L-NW07-L2C-S6800-01', 'ip': '************', 'vendor': 'H3C', 'location': '深圳软基机房'},
    # {'name': 'SZ-JYJF-110607-V-S9850-01', 'ip': '***********53', 'vendor': 'H3C', 'location': '深圳金云机房'},
    # {'name': 'SZ-JYJF-110607-Z-S6890-01', 'ip': '************', 'vendor': 'H3C', 'location': '深圳金云机房'},
    # {'name': 'GZ-LY-3L-1-0113-L2C-S5800-01', 'ip': '************', 'vendor': 'H3C', 'location': '广州连云1期机房'},
    # {'name': 'GZ-LY-3L-1-0114-L2C-S5800-02', 'ip': '************', 'vendor': 'H3C', 'location': '广州连云1期机房'},
    # {'name': 'GZ-LY-3L-1-0113-X-S5800-01', 'ip': '************', 'vendor': 'H3C', 'location': '广州连云1期机房'},
    # {'name': 'GZ-LY-3L-1-0114-X-S5800-02', 'ip': '************', 'vendor': 'H3C', 'location': '广州连云1期机房'},
    # {'name': 'GZ-LY-4F01-A13-L2C-S5800-01', 'ip': '*************', 'vendor': 'H3C', 'location': '广州连云1期机房'},
    # {'name': 'GZ-LY-4F01-A13-L2C-S5800-02', 'ip': '************', 'vendor': 'H3C', 'location': '广州连云1期机房'},
    # {'name': 'GZ-LY-4L-1-0114-X-S5800-05', 'ip': '************', 'vendor': 'H3C', 'location': '广州连云1期机房'},
    # {'name': 'GZ-LY-4L-1-0114-X-S5800-06', 'ip': '************', 'vendor': 'H3C', 'location': '广州连云1期机房'},
    # {'name': 'GZ-LY2-403-1402-L2C-S5560-03', 'ip': '*************', 'vendor': 'H3C', 'location': '广州连云2期机房'},
    # {'name': 'GZ-LY2-403-1403-L2C-S5560-04', 'ip': '*************', 'vendor': 'H3C', 'location': '广州连云2期机房'},
    # {'name': 'GZ-LY2-403-1402-X-S5560-03', 'ip': '*************', 'vendor': 'H3C', 'location': '广州连云2期机房'},
    # {'name': 'GZ-LY2-403-1403-X-S5560-04', 'ip': '*************', 'vendor': 'H3C', 'location': '广州连云2期机房'},
    # {'name': 'GZ-LY2-405-04-B02B-L2C-S5560-01', 'ip': '*************', 'vendor': 'H3C', 'location': '广州连云2期机房'},
    # {'name': 'GZ-LY2-405-04-B02B-L2C-S5560-02', 'ip': '*************', 'vendor': 'H3C', 'location': '广州连云2期机房'},
    # {'name': 'GZ-LY2-405-04-B02B-X-S5800-01', 'ip': '*************', 'vendor': 'H3C', 'location': '广州连云2期机房'},
    # {'name': 'GZ-LY2-405-04-B02B-X-S5800-02', 'ip': '*************', 'vendor': 'H3C', 'location': '广州连云2期机房'},
    # {'name': 'GZ-LY3-305-B02-X-S5800-01', 'ip': '***********79', 'vendor': 'H3C', 'location': '广州连云3期机房'},
    # {'name': 'B_KXC_NI3_S5800_03', 'ip': '***************', 'vendor': 'H3C', 'location': '广州亚太机房'},
    # {'name': 'B_KXC_SI4_S5800_04', 'ip': '***************', 'vendor': 'H3C', 'location': '广州亚太机房'},
    # {'name': 'B-FS-ZHXC-HH08-02-5800-04', 'ip': '***************', 'vendor': 'H3C', 'location': '佛山智慧新城机房'},
    # {'name': 'B-FS-ZHXC-HH08-03-5800-05', 'ip': '***************', 'vendor': 'H3C', 'location': '佛山智慧新城机房'},
    # {'name': 'FS_ShunDe_4F_A_02_03_S5800_01', 'ip': '**************', 'vendor': 'H3C', 'location': '佛山顺德机房'},
    # {'name': 'FS_ShunDe_4F_A_02_03_S5800_02', 'ip': '**************', 'vendor': 'H3C', 'location': '佛山顺德机房'},
    # {'name': 'FS-FN-202-0514-L2c-S5800-01', 'ip': '************', 'vendor': 'H3C', 'location': '佛山福能机房'},
    # {'name': 'FS-TC-5L-502-S5800-01', 'ip': '************', 'vendor': 'H3C', 'location': '佛山天辰机房'},
    # {'name': 'FS-ZHXC-1L-HH24-10-X-S5800-05', 'ip': '*************', 'vendor': 'H3C', 'location': '佛山智慧新城机房'},
    # {'name': 'FS-ZHXC-1L-HH24-10-X-S5800-06', 'ip': '*************', 'vendor': 'H3C', 'location': '佛山智慧新城机房'},
    # {'name': 'FS-ZHXC-5L-HH02-05-X-S5800-03', 'ip': '************', 'vendor': 'H3C', 'location': '佛山智慧新城机房'},
    # {'name': 'FS-ZHXC-HH08-02-S5800-01', 'ip': '************', 'vendor': 'H3C', 'location': '佛山智慧新城机房'},
    # {'name': 'FS-ZHXC-HH08-03-S5800-02', 'ip': '************', 'vendor': 'H3C', 'location': '佛山智慧新城机房'},
    # {'name': 'GZ_YT_3L-1_NI2_S5800_01', 'ip': '*************', 'vendor': 'H3C', 'location': '广州亚太机房'},
    # {'name': 'GZ_YT_3L-1_SI2_S5800_02', 'ip': '*************', 'vendor': 'H3C', 'location': '广州亚太机房'},
    # {'name': 'GZ_YT_4L-1_E6_S5800_03', 'ip': '************', 'vendor': 'H3C', 'location': '广州亚太机房'},
    # {'name': 'GZ_YT_4L-1_E6_S5800_04', 'ip': '************', 'vendor': 'H3C', 'location': '广州亚太机房'},
    # {'name': 'GZ-B-NFJD2-CO3-S5800-01', 'ip': '***************', 'vendor': 'H3C', 'location': '广州南方基地机房'},
    # {'name': 'GZ-B-NFJD2-CO3-S5800-02', 'ip': '***************', 'vendor': 'H3C', 'location': '广州南方基地机房'},
    # {'name': 'GZ-GZL-405-A01-B-S5800-01', 'ip': '***************', 'vendor': 'H3C', 'location': '广州广之旅机房'},
    # {'name': 'GZ-GZL-405-A01-X-S5800-03', 'ip': '*************', 'vendor': 'H3C', 'location': '广州广之旅机房'},
    # {'name': 'GZ-GZL404-A01-L2C-S5800-01', 'ip': '***********', 'vendor': 'H3C', 'location': '广州广之旅机房'},
    # {'name': 'GZ-GZL405-A01-L2C-S5800-01', 'ip': '*************', 'vendor': 'H3C', 'location': '广州广之旅机房'},
    # {'name': 'GZ-KXC-NI2-L2C-S5800-01', 'ip': '**************', 'vendor': 'H3C', 'location': '广州亚太机房'},
    # {'name': 'GZ-NFJD-3L-C03-Z-S5800-01', 'ip': '*************', 'vendor': 'H3C', 'location': '广州南方基地机房'},
    # {'name': 'GZ-NFJD-3L-C03-Z-S5800-02', 'ip': '*************', 'vendor': 'H3C', 'location': '广州南方基地机房'},
    # {'name': 'GZ-QR-4L-403-G01-X-S5800-01', 'ip': '***************', 'vendor': 'H3C', 'location': '广州旗锐机房'},
    # {'name': 'GZ-QR-4L-403-G01-X-S5800-02', 'ip': '***************', 'vendor': 'H3C', 'location': '广州旗锐机房'},
    # {'name': 'GZ-X-NFJD2-CO3-S5800-01', 'ip': '************', 'vendor': 'H3C', 'location': '广州南方基地机房'},
    # {'name': 'GZ-X-NFJD2-CO3-S5800-02', 'ip': '************', 'vendor': 'H3C', 'location': '广州南方基地机房'},
    # {'name': 'GZ-ZXJF-3L-3-5-L2C-S5800-01', 'ip': '************', 'vendor': 'H3C', 'location': '广州中信机房'},
    # {'name': 'KXC_NI1_RRPP_S5800_01', 'ip': '***************', 'vendor': 'H3C', 'location': '广州亚太机房'},
    # {'name': 'SC-CDJF-IDC2-D13-X-S5800-01', 'ip': '***************', 'vendor': 'H3C', 'location': '四川成都机房'},
    # {'name': 'SC-CDJF-IDC2-D13-X-S5800-02', 'ip': '***************', 'vendor': 'H3C', 'location': '四川成都机房'},
    # {'name': 'SZ-HYC-051-5800-07', 'ip': '************', 'vendor': 'H3C', 'location': '深圳花园城机房'},
    # {'name': 'SZ-HYC-052-5800-06', 'ip': '************', 'vendor': 'H3C', 'location': '深圳花园城机房'},
    # {'name': 'SZ-HYC-1L-1-051-B-S5800-12', 'ip': '**************', 'vendor': 'H3C', 'location': '深圳花园城机房'},
    # {'name': 'SZ-HYC-1L-1-051-X-S5800-13', 'ip': '************', 'vendor': 'H3C', 'location': '深圳花园城机房'},
    # {'name': 'SZ-HYC-1L-1-052-B-S5800-11', 'ip': '**************', 'vendor': 'H3C', 'location': '深圳花园城机房'},
    # {'name': 'SZ-HYC-1L-1-052-X-S5800-14', 'ip': '************', 'vendor': 'H3C', 'location': '深圳花园城机房'},
    # {'name': 'SZ-HYC-1L-1-052-Z-S5800-01', 'ip': '************', 'vendor': 'H3C', 'location': '深圳花园城机房'},
    # {'name': 'SZ-HYC-1L-1-052-Z-S5800-02', 'ip': '************', 'vendor': 'H3C', 'location': '深圳花园城机房'},
    # {'name': 'SZ-HYC-249-S5800-04', 'ip': '************', 'vendor': 'H3C', 'location': '深圳花园城机房'},
    # {'name': 'SZ-HYC-250-S5800-03', 'ip': '************', 'vendor': 'H3C', 'location': '深圳花园城机房'},
    # {'name': 'SZ-JYJF-110607-L2C-S5800-01', 'ip': '************', 'vendor': 'H3C', 'location': '深圳金云机房'},
    # {'name': 'SZ-RJJD-4L-NW06-L2C-S5800-01', 'ip': '*************', 'vendor': 'H3C', 'location': '深圳软基机房'},
    # {'name': 'SZ-RJJD-4L-NW07-L2C-S5800-02', 'ip': '***********6', 'vendor': 'H3C', 'location': '深圳软基机房'},
    # {'name': 'SZ-RJJD-4L-NW11-X-5800-01', 'ip': '************', 'vendor': 'H3C', 'location': '深圳软基机房'},
    # {'name': 'SZ-RJJD-4L-NW11-X-5800-02', 'ip': '************', 'vendor': 'H3C', 'location': '深圳软基机房'},
    # {'name': 'SZ-RJJD-503-C56-X-S5800-03', 'ip': '*************', 'vendor': 'H3C', 'location': '深圳软基机房'},
    # {'name': 'SZ-RJJD-503-C56-X-S5800-04', 'ip': '************', 'vendor': 'H3C', 'location': '深圳软基机房'},
    # {'name': 'FS-FN-303-05-11-X-S5560-01', 'ip': '*************', 'vendor': 'H3C', 'location': '佛山福能机房'},
    # {'name': 'FS-FN-303-05-11-X-S5560-02', 'ip': '*************', 'vendor': 'H3C', 'location': '佛山福能机房'},
    # {'name': 'FS-SD-4F-A-02-3-V-S5560-01', 'ip': '************', 'vendor': 'H3C', 'location': '佛山顺德机房'},
    # {'name': 'FS-SD-4F-A-02-3-V-S5560-02', 'ip': '************', 'vendor': 'H3C', 'location': '佛山顺德机房'},
    # {'name': 'FS-ZHXC-1L-8-HH24-10-V-S5560-01', 'ip': '************', 'vendor': 'H3C', 'location': '佛山智慧新城机房'},
    # {'name': 'FS-ZHXC-1L-8-HH24-10-V-S5560-02', 'ip': '************', 'vendor': 'H3C', 'location': '佛山智慧新城机房'},
    # {'name': 'FS-ZHXC-3L-HH08-03-L2C-S5560-01', 'ip': '*************', 'vendor': 'H3C', 'location': '佛山智慧新城机房'},
    # {'name': 'FS-ZHXC-5L-HH02-05-X-S5560-01', 'ip': '************', 'vendor': 'H3C', 'location': '佛山智慧新城机房'},
    # {'name': 'FS-ZHXC-5L-HH02-05-X-S5560-02', 'ip': '************', 'vendor': 'H3C', 'location': '佛山智慧新城机房'},
    # {'name': 'GZ-LY2-403-1402-L2C-S5560-03', 'ip': '*************', 'vendor': 'H3C', 'location': '广州连云2期机房'},
    # {'name': 'GZ-LY2-403-1403-L2C-S5560-04', 'ip': '*************', 'vendor': 'H3C', 'location': '广州连云2期机房'},
    # {'name': 'GZ-LY2-403-1403-X-S5560-04', 'ip': '*************', 'vendor': 'H3C', 'location': '广州连云2期机房'},
    # {'name': 'GZ-LY2-405-04-B02B-L2C-S5560-01', 'ip': '*************', 'vendor': 'H3C', 'location': '广州连云2期机房'},
    # {'name': 'GZ-LY2-405-04-B02B-L2C-S5560-02', 'ip': '*************', 'vendor': 'H3C', 'location': '广州连云2期机房'},
    # {'name': 'GZ-NFJD-3L-B03-L2C-S5560-01', 'ip': '************', 'vendor': 'H3C', 'location': '广州南方基地机房'},
    # {'name': 'GZ-NFJD-3L-B03-L2C-S5560-02', 'ip': '************', 'vendor': 'H3C', 'location': '广州南方基地机房'},
    # {'name': 'GZ-NFJD-3L-B03-V-S5560-03', 'ip': '************', 'vendor': 'H3C', 'location': '广州南方基地机房'},
    # {'name': 'GZ-NFJD-3L-B03-V-S5560-04', 'ip': '************', 'vendor': 'H3C', 'location': '广州南方基地机房'},
    # {'name': 'GZ-ZXJF-3L-3-5-L2C-S5560-01', 'ip': '************', 'vendor': 'H3C', 'location': '广州中信机房'},
    # {'name': 'SC-CDJF-IDC5-D13-40U-X-S5560-02', 'ip': '***************', 'vendor': 'H3C', 'location': '四川成都机房'},
    # {'name': 'SC-CDJF-IDC5-D13-42U-X-S5560-01', 'ip': '***************', 'vendor': 'H3C', 'location': '四川成都机房'},
    # {'name': 'SZ-BWX-1L-A09-L2C-S5560-01', 'ip': '*************', 'vendor': 'H3C', 'location': '深圳百旺信机房'},
    # {'name': 'SZ-HYC-1L-2-246-S5560-03', 'ip': '***************', 'vendor': 'H3C', 'location': '深圳花园城机房'},
    # {'name': 'SZ-HYC-246-L2C-S5560-01', 'ip': '************', 'vendor': 'H3C', 'location': '深圳花园城机房'},
    # {'name': 'SZ-HYC-250-L2C-S5560-02', 'ip': '*************', 'vendor': 'H3C', 'location': '深圳花园城机房'},
    # {'name': 'SZ-RJJD-4L-NW07-L2C-S5560-01', 'ip': '***********', 'vendor': 'H3C', 'location': '深圳软基机房'},
    # {'name': 'SZ-YT-A09-V-S5560-01', 'ip': '***********54', 'vendor': 'H3C', 'location': '深圳盐田机房'},
    # {'name': 'SZ-YT-A09-V-S5560-02', 'ip': '***********45', 'vendor': 'H3C', 'location': '深圳盐田机房'},
    # {'name': 'SZ-HYC-250-L2C-S5560-02', 'ip': '*************', 'vendor': 'H3C', 'location': '深圳花园城机房'},
    # 华为设备
    {'name': 'GZ-LY-3L-1-0113-X-S8861-01', 'ip': '*************', 'vendor': 'HW', 'location': '广州连云1期机房'},
    # {'name': 'GZ-LY-3L-1-0114-X-S8861-02', 'ip': '*************', 'vendor': 'HW', 'location': '广州连云1期机房'},
    # {'name': 'GZ-LY-3L-1-0113-Z-S8861-03', 'ip': '***********', 'vendor': 'HW', 'location': '广州连云1期机房'},
    # {'name': 'GZ-LY-3L-1-0114-Z-S8861-04', 'ip': '***********', 'vendor': 'HW', 'location': '广州连云1期机房'},
    # {'name': 'GZ-LY2-403-1402-Z-S8861-01', 'ip': '*************', 'vendor': 'HW', 'location': '广州连云2期机房'},
    # {'name': 'GZ-LY2-403-1403-Z-S8861-02', 'ip': '*************', 'vendor': 'HW', 'location': '广州连云2期机房'},
    # {'name': 'GZ-LY2-403-1402-X-S8861-03', 'ip': '*************', 'vendor': 'HW', 'location': '广州连云2期机房'},
    # {'name': 'GZ-LY2-403-1403-X-S8861-04', 'ip': '*************', 'vendor': 'HW', 'location': '广州连云2期机房'},
    # {'name': 'GZ-LY3-305-B02-Z-S8861-01', 'ip': '***********83', 'vendor': 'HW', 'location': '广州连云3期机房'},
    # {'name': 'GZ-LY3-305-B03-Z-S8861-02', 'ip': '***********80', 'vendor': 'HW', 'location': '广州连云3期机房'},
    # {'name': 'FS-ZHXC-3L-HH08-02-Z-S8861-01', 'ip': '*************', 'vendor': 'HW', 'location': '佛山智慧新城机房'},
    # {'name': 'FS-ZHXC-3L-HH08-03-Z-S8861-02', 'ip': '*************', 'vendor': 'HW', 'location': '佛山智慧新城机房'},
    # {'name': 'GZ-GDLJF-105-101-X-CE8861-01', 'ip': '**************', 'vendor': 'HW', 'location': '广州云泰机房'},
    # {'name': 'GZ-GDLJF-105-101-X-CE8861-02', 'ip': '**************', 'vendor': 'HW', 'location': '广州云泰机房'},
    # {'name': 'GZ-PYGC-13L-02-test-FM8861-01', 'ip': '************', 'vendor': 'HW', 'location': '广州平云广场机房'},
    # {'name': 'SC-CDJF-IDC2-D13-X-S8861-01', 'ip': '***************', 'vendor': 'HW', 'location': '四川成都机房'},
    # {'name': 'SC-CDJF-IDC2-D13-X-S8861-02', 'ip': '***************', 'vendor': 'HW', 'location': '四川成都机房'},
    # {'name': 'SC-CDJF-IDC2-D13-Z-S8861-03', 'ip': '***************', 'vendor': 'HW', 'location': '四川成都机房'},
    # {'name': 'SC-CDJF-IDC2-D13-Z-S8861-04', 'ip': '***************', 'vendor': 'HW', 'location': '四川成都机房'},
    # {'name': 'SZ-JYJF-110607-V2-CE8861-01', 'ip': '************', 'vendor': 'HW', 'location': '深圳金云机房'},
]

# SNMP社区字符串配置
SNMP_COMMUNITY = {
    'H3C': '21vianetnms',
    'HW': '21vianetnms'
}

# 邮件配置
SMTP_SERVER = 'mail.21nmc.com'
SMTP_PORT = 25
SENDER_EMAIL = '<EMAIL>'
SENDER_PASSWORD = 'NMC_21vianet.hn'
RECIPIENTS = ['<EMAIL>','<EMAIL>']

# OID配置
OID_CONFIG = {
    'H3C': {
        'port_ifdescr': '*******.*******.1.2',
        'module_present': '*******.4.1.25506.********.1.8',
        'module_status': '*******.*******.1.8',
        'module_vendor': '*******.4.1.25506.********.1.4',
        'module_model': '*******.4.1.25506.********.1.2',
        'module_wavelen': '*******.4.1.25506.********.1.3'
    },
    'HW': {
        'port_ifdescr': '*******.*******.1.2',
        'port_oper_status': '*******.*******.1.8',
        'port_name': '*******.4.1.2011.*********.********',
        'module_present': '*******.4.1.2011.*********.********',
        'module_vendor': '*******.4.1.2011.*********.*******4',
        'module_model': '*******.4.1.2011.*********.********',
        'module_wavelen': '*******.4.1.2011.*********.*******'
    }
}


# ================== SNMP 功能函数 ==================
def snmp_get(ip, oid, community):
    """通用SNMP GET操作"""
    iterator = getCmd(
        SnmpEngine(),
        CommunityData(community),
        UdpTransportTarget((ip, 161), timeout=5.0),
        ContextData(),
        ObjectType(ObjectIdentity(oid))
    )
    try:
        errorIndication, errorStatus, errorIndex, varBinds = next(iterator)
        if errorIndication:
            return None
        return str(varBinds[0][1]) if varBinds else None
    except Exception:
        return None


def snmp_walk(ip, oid_base, community):
    """通用SNMP WALK操作"""
    result = {}
    iterator = nextCmd(
        SnmpEngine(),
        CommunityData(community),
        UdpTransportTarget((ip, 161), timeout=5.0),
        ContextData(),
        ObjectType(ObjectIdentity(oid_base)),
        lexicographicMode=False
    )
    try:
        for response in iterator:
            errorIndication, errorStatus, errorIndex, varBinds = response
            if errorIndication:
                break
            for varBind in varBinds:
                oid_parts = varBind[0].getOid()
                idx = oid_parts[-1]
                result[idx] = str(varBind[1])
    except Exception:
        pass
    return result


def check_snmp_connection(ip, community):
    """检查SNMP连通性"""
    sys_oid = '*******.*******.0'
    return snmp_get(ip, sys_oid, community) is not None


# ================== 设备处理函数 ==================
def process_h3c_device(device):
    """处理H3C设备"""
    ip = device['ip']
    community = SNMP_COMMUNITY['H3C']
    data = []
    location = device.get('location', '未知机房')

    if not check_snmp_connection(ip, community):
        return [], {'name': device['name'], 'ip': ip, 'error': 'SNMP连接失败'}

    port_names = snmp_walk(ip, OID_CONFIG['H3C']['port_ifdescr'], community)
    present_status = snmp_walk(ip, OID_CONFIG['H3C']['module_present'], community)
    optical_ports = [idx for idx, val in present_status.items() if val == '1']

    for port_idx in optical_ports:
        port_name = port_names.get(port_idx, f'未知端口-{port_idx}')
        status = snmp_get(ip, f"{OID_CONFIG['H3C']['module_status']}.{port_idx}", community)
        status = '在用' if status == '1' else '空闲' if status == '2' else '未知状态'
        vendor = snmp_get(ip, f"{OID_CONFIG['H3C']['module_vendor']}.{port_idx}", community) or '未知厂商'
        model = snmp_get(ip, f"{OID_CONFIG['H3C']['module_model']}.{port_idx}", community) or '未知型号'
        wavelength = snmp_get(ip, f"{OID_CONFIG['H3C']['module_wavelen']}.{port_idx}", community) or '未知波长'

        data.append({
            'location': location,
            'device': device['name'],
            'ip': ip,
            'port': port_name,
            'status': status,
            'vendor': vendor,
            'model': model,
            'wavelength': wavelength
        })
    return data, None


def process_hw_device(device):
    """处理华为设备"""
    ip = device['ip']
    community = SNMP_COMMUNITY['HW']
    data = []
    location = device.get('location', '未知机房')

    if not check_snmp_connection(ip, community):
        return [], {'name': device['name'], 'ip': ip, 'error': 'SNMP连接失败'}

    if_descr_map = snmp_walk(ip, OID_CONFIG['HW']['port_ifdescr'], community)
    hw_port_names = snmp_walk(ip, OID_CONFIG['HW']['port_name'], community)
    port_mapping = {hw_id: idx for idx, name in if_descr_map.items() for hw_id, hw_name in hw_port_names.items() if
                    hw_name == name}
    present_status = snmp_walk(ip, OID_CONFIG['HW']['module_present'], community)
    optical_ports = [hw_id for hw_id, val in present_status.items() if val != '0']

    for hw_id in optical_ports:
        if_index = port_mapping.get(hw_id)
        if not if_index:
            continue

        status = snmp_get(ip, f"{OID_CONFIG['HW']['port_oper_status']}.{if_index}", community)
        status = '在用' if status == '1' else '空闲' if status == '2' else '未知状态'
        vendor = snmp_get(ip, f"{OID_CONFIG['HW']['module_vendor']}.{hw_id}", community) or '未知厂商'
        model = snmp_get(ip, f"{OID_CONFIG['HW']['module_model']}.{hw_id}", community) or '未知型号'
        wavelength = snmp_get(ip, f"{OID_CONFIG['HW']['module_wavelen']}.{hw_id}", community) or '未知波长'

        data.append({
            'location': location,
            'device': device['name'],
            'ip': ip,
            'port': hw_port_names.get(hw_id, f'未知端口-{hw_id}'),
            'status': status,
            'vendor': vendor,
            'model': model,
            'wavelength': wavelength
        })
    return data, None


# ================== 数据收集与报告生成 ==================
def collect_data():
    """多线程收集数据"""
    all_data = []
    failed_devices = []
    with concurrent.futures.ThreadPoolExecutor(max_workers=100) as executor:
        futures = []
        for device in DEVICES:
            if device['vendor'] == 'H3C':
                futures.append(executor.submit(process_h3c_device, device))
            else:
                futures.append(executor.submit(process_hw_device, device))

        for future in concurrent.futures.as_completed(futures):
            data, failed = future.result()
            all_data.extend(data)
            if failed:
                failed_devices.append(failed)
    return all_data, failed_devices


def generate_idle_modules_csv(data, filename):
    """生成空闲模块CSV报告"""
    # 筛选空闲模块
    idle_modules = [d for d in data if d['status'] == '空闲']

    # 统计型号和波长组合的数量
    module_stats = defaultdict(int)
    for module in idle_modules:
        key = (module['model'], module['wavelength'])
        module_stats[key] += 1

    # 排序: 按数量降序，然后按型号和波长升序
    sorted_stats = sorted(module_stats.items(),
                          key=lambda x: (-x[1], x[0][0], x[0][1]))

    # 写入CSV文件
    with open(filename, 'w', newline='', encoding='utf-8-sig') as f:
        writer = csv.writer(f)
        writer.writerow(['模块型号', '波长(nm)', '数量'])
        for (model, wavelength), count in sorted_stats:
            writer.writerow([model, wavelength, count])

    return filename, sorted_stats


def generate_report(data, failed_devices):
    """生成统计报告和CSV文件"""
    date_str = datetime.now().strftime("%Y%m%d")
    report_filename = f"Optical_Report_{date_str}.csv"
    idle_report_filename = f"Idle_Modules_Report_{date_str}.csv"

    # 写入主报表文件
    with open(report_filename, 'w', newline='', encoding='utf-8-sig') as f:
        writer = csv.writer(f)
        writer.writerow(['机房', '设备名称', 'IP地址', '端口', '状态', '厂商', '型号', '波长(nm)'])
        for item in data:
            writer.writerow([
                item['location'],
                item['device'],
                item['ip'],
                item['port'],
                item['status'],
                item['vendor'],
                item['model'],
                item['wavelength']
            ])

    # 生成空闲模块报表
    idle_report_file, idle_stats = generate_idle_modules_csv(data, idle_report_filename)

    # 重构统计数据结构
    stats = {
        'total_devices': len(DEVICES),
        'success_devices': len(DEVICES) - len(failed_devices),
        'failed_devices': len(failed_devices),
        'total_modules': len(data),
        'used': sum(1 for d in data if d['status'] == '在用'),
        'idle': sum(1 for d in data if d['status'] == '空闲'),
        'location_group': defaultdict(list),
        'failed_details': failed_devices,
        'idle_modules_stats': idle_stats,
        'idle_report_file': idle_report_file,
        'report_file': report_filename
    }

    # 按机房分组统计空闲模块
    idle_modules = [d for d in data if d['status'] == '空闲']
    temp_stats = defaultdict(int)

    for module in idle_modules:
        key = (module['location'], module['model'], module['wavelength'])
        temp_stats[key] += 1

    # 转换数据结构并排序
    for (loc, model, wave), count in temp_stats.items():
        stats['location_group'][loc].append({
            'model': model,
            'wavelength': wave,
            'count': count
        })

    # 按数量降序、型号和波长升序排序
    for loc in stats['location_group']:
        stats['location_group'][loc].sort(
            key=lambda x: (-x['count'], x['model'], x['wavelength'])
        )

    return stats


# ================== 邮件发送功能 ==================
def send_email(stats):
    """发送带增强统计的邮件"""
    msg = MIMEMultipart()
    msg['From'] = SENDER_EMAIL
    msg['To'] = ', '.join(RECIPIENTS)
    msg['Subject'] = f"光模块使用报告 {datetime.now().strftime('%Y-%m-%d')}"

    # 样式定义
    table_style = """
    <style>
    .data-table {
        border-collapse: collapse;
        width: 100%;
        margin: 20px 0;
        font-family: Arial, sans-serif;
    }
    .data-table th, .data-table td {
        border: 1px solid #ddd;
        padding: 12px;
        text-align: left;
    }
    .data-table tr:nth-child(even){background-color: #f8f9fa;}
    .data-table th {
        background-color: #4CAF50;
        color: white;
        padding: 15px;
    }
    .location-cell {
        background-color: #e9f5e9;
        font-weight: bold;
    }
    .total-row {
        background-color: #e3f2fd !important;
        font-weight: bold;
    }
    .sub-header th {
        background-color: #81C784;
    }
    h2 {
        color: #2c3e50;
        border-bottom: 2px solid #4CAF50;
        padding-bottom: 10px;
    }
    </style>
    """

    # 生成核心统计表格
    core_stats = f"""
    <h2>核心统计指标</h2>
    <table class="data-table">
        <tr>
            <th>总设备数</th><td>{stats['total_devices']}</td>
            <th>成功采集数</td><td>{stats['success_devices']}</td>
        </tr>
        <tr>
            <th>SNMP失败数</th><td>{stats['failed_devices']}</td>
            <th>总模块数</td><td>{stats['total_modules']}</td>
        </tr>
        <tr>
            <th>在用模块数</th><td>{stats['used']}</td>
            <th>空闲模块数</td><td>{stats['idle']}</td>
        </tr>
    </table>
    """

    # 生成全局空闲模块统计表
    idle_global_table = """
    <h2>全局空闲模块统计</h2>
    <table class="data-table">
        <tr>
            <th>模块型号</th>
            <th>波长(nm)</th>
            <th>数量</th>
        </tr>
    """
    for (model, wavelength), count in stats['idle_modules_stats']:
        idle_global_table += f"""
        <tr>
            <td>{model}</td>
            <td>{wavelength}</td>
            <td>{count}</td>
        </tr>
        """
    idle_global_table += "</table>"

    # 生成机房维度的空闲模块统计表
    location_tables = []
    for loc, modules in stats['location_group'].items():
        if not modules:
            continue

        total = sum(m['count'] for m in modules)
        table_rows = []

        # 表头
        table_rows.append("""
        <tr class="sub-header">
            <th>机房</th>
            <th>型号</th>
            <th>波长(nm)</th>
            <th>数量</th>
            <th>总计</th>
        </tr>
        """)

        # 模块明细行
        for i, m in enumerate(modules):
            row = f"""
            <tr>
                <td class="location-cell">{loc if i == 0 else ''}</td>
                <td>{m['model']}</td>
                <td>{m['wavelength']}</td>
                <td>{m['count']}</td>
                <td class="total-row">{total if i == 0 else ''}</td>
            </tr>
            """
            table_rows.append(row)

        location_tables.append(f"""
        <h3>
            {loc}
        </h3>
        <table class="data-table">
            {''.join(table_rows)}
        </table>
        """)

    # 生成失败设备表格
    failed_table = ""
    if stats['failed_devices'] > 0:
        failed_table = """
        <h3>SNMP连接失败设备列表</h3>
        <table class="data-table">
            <tr>
                <th>设备名称</th>
                <th>IP地址</th>
                <th>错误原因</th>
            </tr>
        """
        for d in stats['failed_details']:
            failed_table += f"""
            <tr>
                <td>{d['name']}</td>
                <td>{d['ip']}</td>
                <td>{d['error']}</td>
            </tr>
            """
        failed_table += "</table>"

    # HTML模板
    html = f"""<html>
    <head>{table_style}</head>
    <body>
        {core_stats}
        {idle_global_table}
        <h2>按机房统计的空闲模块</h2>
        {''.join(location_tables)}
        {failed_table if stats['failed_devices'] > 0 else ''}
    </body>
    </html>"""

    # 保存HTML内容到文件
    with open('结果正文.html', 'w', encoding='utf-8') as html_file:
        html_file.write(html)
    print("已保存HTML文件：结果正文.html")

    msg.attach(MIMEText(html, 'html'))

    # 添加主报表文件附件
    with open(stats['report_file'], "rb") as f:
        part = MIMEApplication(f.read(), Name=stats['report_file'])
        part['Content-Disposition'] = f'attachment; filename="{stats["report_file"]}"'
        msg.attach(part)

    # 添加空闲模块报表附件
    with open(stats['idle_report_file'], "rb") as f:
        part = MIMEApplication(f.read(), Name=stats['idle_report_file'])
        part['Content-Disposition'] = f'attachment; filename="{stats["idle_report_file"]}"'
        msg.attach(part)

    # 发送邮件
    with smtplib.SMTP(SMTP_SERVER, SMTP_PORT) as server:
        server.starttls()
        server.login(SENDER_EMAIL, SENDER_PASSWORD)
        server.sendmail(SENDER_EMAIL, RECIPIENTS, msg.as_string())


# ================== 主程序 ==================
if __name__ == '__main__':
    print(f"{datetime.now()} 开始数据采集...")
    all_data, failed_devices = collect_data()
    print(f"{datetime.now()} 生成统计报告...")
    statistics = generate_report(all_data, failed_devices)
    print(f"{datetime.now()} 发送邮件通知...")
    send_email(statistics)
    print(f"{datetime.now()} 任务完成，报表文件：{statistics['report_file']}")
    print(f"{datetime.now()} 空闲模块报表文件：{statistics['idle_report_file']}")