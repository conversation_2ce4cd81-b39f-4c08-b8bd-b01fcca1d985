"""
Django settings for script_platform project.

Generated by 'django-admin startproject' using Django 3.2.3.

For more information on this file, see
https://docs.djangoproject.com/en/3.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/3.2/ref/settings/
"""

from pathlib import Path
import os

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/3.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-h)ia-p0bto^z^tz7=%nw^@z9g_wvum@te%8ktm!h*u7m*c5=yl'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = []


# Application definition

INSTALLED_APPS = [
    'simpleui',  # SimpleUI必须放在第一位
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    # 'django_celery_beat',  # 暂时注释掉
    # 'django_celery_results',  # 暂时注释掉
    'script_manager',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'script_platform.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [os.path.join(BASE_DIR, 'templates')],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'script_platform.wsgi.application'


# Database
# https://docs.djangoproject.com/en/3.2/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
    # MySQL配置（生产环境使用）
    # 'default': {
    #     'ENGINE': 'django.db.backends.mysql',
    #     'NAME': 'script_platform',
    #     'USER': 'root',
    #     'PASSWORD': 'password',  # 请根据实际情况修改
    #     'HOST': 'localhost',
    #     'PORT': '3306',
    #     'OPTIONS': {
    #         'charset': 'utf8mb4',
    #     },
    # }
}


# Password validation
# https://docs.djangoproject.com/en/3.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/3.2/topics/i18n/

LANGUAGE_CODE = 'zh-hans'

TIME_ZONE = 'Asia/Shanghai'

USE_I18N = True

USE_L10N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/3.2/howto/static-files/

STATIC_URL = '/static/'

# Default primary key field type
# https://docs.djangoproject.com/en/3.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# SimpleUI 配置
SIMPLEUI_DEFAULT_THEME = 'admin.lte.css'  # 使用AdminLTE主题
SIMPLEUI_HOME_PAGE = '/admin/'  # 设置首页
SIMPLEUI_HOME_TITLE = 'Python脚本执行平台'  # 首页标题
SIMPLEUI_HOME_ICON = 'fas fa-code'  # 首页图标
SIMPLEUI_LOGO = 'https://avatars2.githubusercontent.com/u/13655483?s=60&v=4'  # Logo

# 自定义菜单
SIMPLEUI_CONFIG = {
    'system_keep': False,  # 不保留系统菜单
    'menu_display': ['脚本管理', '执行记录', '定时任务', '系统日志'],  # 显示的菜单
    'dynamic': True,  # 动态菜单
    'menus': [
        {
            'name': '脚本管理',
            'icon': 'fas fa-file-code',
            'models': [
                {
                    'name': '脚本列表',
                    'icon': 'fas fa-list',
                    'url': '/admin/script_manager/script/'
                },
                {
                    'name': '添加脚本',
                    'icon': 'fas fa-plus',
                    'url': '/admin/script_manager/script/add/'
                }
            ]
        },
        {
            'name': '执行管理',
            'icon': 'fas fa-play-circle',
            'models': [
                {
                    'name': '执行记录',
                    'icon': 'fas fa-history',
                    'url': '/admin/script_manager/executionrecord/'
                },
                {
                    'name': '定时任务',
                    'icon': 'fas fa-clock',
                    'url': '/admin/script_manager/scheduledtask/'
                }
            ]
        },
        {
            'name': '系统监控',
            'icon': 'fas fa-chart-line',
            'models': [
                {
                    'name': '系统日志',
                    'icon': 'fas fa-file-alt',
                    'url': '/admin/script_manager/systemlog/'
                },
                {
                    'name': '平台概览',
                    'icon': 'fas fa-tachometer-alt',
                    'url': '/script/dashboard/'
                }
            ]
        }
    ]
}

# 媒体文件配置
MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

# 静态文件配置
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')
STATICFILES_DIRS = [
    os.path.join(BASE_DIR, 'static'),
]

# Celery配置 - 暂时注释掉
# CELERY_BROKER_URL = 'redis://localhost:6379/0'
# CELERY_RESULT_BACKEND = 'django-db'
# CELERY_CACHE_BACKEND = 'django-cache'
# CELERY_ACCEPT_CONTENT = ['json']
# CELERY_TASK_SERIALIZER = 'json'
# CELERY_RESULT_SERIALIZER = 'json'
# CELERY_TIMEZONE = TIME_ZONE

# SimpleUI配置 - 暂时注释掉
# SIMPLEUI_CONFIG = {
#     'system_keep': False,
#     'menu_display': ['脚本管理', '任务调度', '执行记录', '用户管理'],
#     'dynamic': True,
#     'tabbed': True,
#     'theme': 'admin.lte.css',
#     'logo': 'https://avatars2.githubusercontent.com/u/13655483?s=60&v=4',
#     'icon': {
#         'script_manager.Script': 'fas fa-code',
#         'script_manager.ScriptParameter': 'fas fa-cog',
#         'script_manager.ScheduledTask': 'fas fa-clock',
#         'script_manager.ExecutionRecord': 'fas fa-history',
#         'auth.User': 'fas fa-user',
#         'auth.Group': 'fas fa-users',
#     }
# }

# 脚本执行配置
SCRIPT_EXECUTION_TIMEOUT = 3600  # 脚本执行超时时间（秒）
MAX_CONCURRENT_SCRIPTS = 10  # 最大并发执行脚本数
SCRIPT_UPLOAD_PATH = 'scripts/'  # 脚本上传路径
SCRIPT_OUTPUT_PATH = 'outputs/'  # 脚本输出文件路径

# 新增：脚本文件组织配置
SCRIPT_INDIVIDUAL_FOLDERS = True  # 每个脚本使用独立文件夹
SCRIPT_EXECUTION_SUBFOLDER = 'executions'  # 执行结果子文件夹名称
REQUIRED_HTML_RESULT_FILE = '结果正文.html'  # 必需的HTML结果文件名
